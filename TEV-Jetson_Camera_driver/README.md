[![Technexion](https://github.com/user-attachments/assets/d20ae328-b4ff-4152-b4dd-6753674d0fe8)](https://www.technexion.com/products/embedded-vision/)

[![Producer: Technexion](https://img.shields.io/badge/Producer-Technexion-blue.svg)](https://www.technexion.com)
[![License: GPL v2](https://img.shields.io/badge/License-GPL%20v2-blue.svg)](https://www.gnu.org/licenses/old-licenses/gpl-2.0.en.html)

## Introduction

[TechNexion Embedded Vision Solutions](https://www.technexion.com/products/embedded-vision/) provide embedded system developers access to high-performance, industrial-grade camera solutions to accelerate their time to market for embedded vision projects.

---

## Supported JetPack Version

- [JetPack 6.1](https://developer.nvidia.com/embedded/jetpack-sdk-61) [[L4T 36.4]](https://developer.nvidia.com/embedded/jetson-linux-r3640)
- [JetPack 6.2](https://developer.nvidia.com/embedded/jetpack-sdk-62) [[L4T 36.4.3]](https://developer.nvidia.com/embedded/jetson-linux-r3643)

</br>

>**Note**: If You'd like to find other supported JetPack versions, you should switch to the other branches:
>- [JetPack 4.6.1](https://github.com/TechNexion-Vision/TEV-Jetson_Camera_driver/tree/tn_l4t-r32.7.1_kernel-4.9)
>- [JetPack 5.1.x](https://github.com/TechNexion-Vision/TEV-Jetson_Camera_driver/tree/tn_l4t-r35.3.1.ga_kernel-5.10)

## Supported Camera Modules

| **Camera Series** | Interface |
| --- | --- |
| [TEVS](https://www.technexion.com/products/embedded-vision/) | MIPI-CSI2 |
| [VLS3](https://www.technexion.com/products/serdes/fpd-link/) | FPD-LINK III |
| [VLS-GM2](https://www.technexion.com/products/serdes/gmsl/gmsl2/) | GMSL2 |

## Supported NVIDIA Jetson Developer Kit

- [NVIDIA Jetson Orin NANO](https://developer.nvidia.com/embedded/learn/get-started-jetson-orin-nano-devkit)

## Supported TechNexion NVIDIA TEK Series

- [TEK6040-ORIN-NANO](https://www.technexion.com/products/embedded-computing/aivision/tek6040-orin-nano/)
- [TEK6100-ORIN-NX](https://www.technexion.com/products/embedded-computing/aivision/tek6100-orin-nx/)

## WIKI Pages

- [Getting Started](https://developer.technexion.com/docs/embedded-vision/tevs/usage-guides/nvidia/quick-start/technexion-camera-modules-for-jetpack-6x)
- [Build Guide](https://developer.technexion.com/docs/embedded-vision/tevs/usage-guides/nvidia/build-guides/nvidia-how-to-build-technexion-camera-drivers-for-jetpack6x)
