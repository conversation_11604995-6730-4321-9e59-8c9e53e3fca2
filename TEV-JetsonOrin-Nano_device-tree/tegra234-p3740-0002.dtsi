// SPDX-License-Identifier: GPL-2.0

#include <dt-bindings/sound/rt5640.h>

/ {
	compatible = "nvidia,p3740-0002";

	bus@0 {
		aconnect@2900000 {
			ahub@2900800 {
				i2s@2901300 {
					ports {
						port@1 {
							endpoint {
								dai-format = "i2s";
								remote-endpoint = <&rt5640_ep>;
							};
						};
					};
				};

				i2s@2901500 {
					ports {
						port@1 {
							endpoint {
								bitclock-master;
								frame-master;
							};
						};
					};
				};
			};
		};

		i2c@31c0000 {
			rt5640: audio-codec@1c {
				compatible = "realtek,rt5640";
				reg = <0x1c>;

				clocks = <&bpmp TEGRA234_CLK_AUD_MCLK>;
				clock-names = "mclk";

				realtek,dmic1-data-pin = <RT5640_DMIC1_DATA_PIN_NONE>;
				realtek,dmic2-data-pin = <RT5640_DMIC2_DATA_PIN_NONE>;
				realtek,jack-detect-source = <RT5640_JD_SRC_HDA_HEADER>;

				/* Codec IRQ output */
				interrupt-parent = <&gpio>;
				interrupts = <TEGRA234_MAIN_GPIO(F, 3) GPIO_ACTIVE_HIGH>;

				#sound-dai-cells = <1>;
				sound-name-prefix = "CVB-RT";

				status = "okay";

				port {
					rt5640_ep: endpoint {
						remote-endpoint = <&i2s4_dap>;
						mclk-fs = <256>;
					};
				};
			};

			/* carrier board ID EEPROM */
			eeprom@55 {
				compatible = "atmel,24c02";
				reg = <0x55>;

				label = "system";
				vcc-supply = <&vdd_1v8_ls>;
				address-width = <8>;
				pagesize = <8>;
				size = <256>;
				read-only;
			};
		};

		padctl@3520000 {
			vclamp-usb-supply = <&vdd_1v8_ao>;
			avdd-usb-supply = <&vdd_3v3_ao>;
			status = "okay";

			pads {
				usb2 {
					lanes {
						usb2-0 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb2-1 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb2-2 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb2-3 {
							nvidia,function = "xusb";
							status = "okay";
						};
					};
				};

				usb3 {
					lanes {
						usb3-0 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb3-1 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb3-2 {
							nvidia,function = "xusb";
							status = "okay";
						};
					};
				};
			};

			ports {
				usb2-0 {
					mode = "otg";
					usb-role-switch;
					status = "okay";
					vbus-supply = <&vdd_5v0_sys>;
				};

				usb2-1 {
					mode = "host";
					status = "okay";
					vbus-supply = <&vdd_5v0_sys>;
				};

				usb2-2 {
					mode = "host";
					status = "okay";
					vbus-supply = <&vdd_5v0_sys>;
				};

				usb2-3 {
					mode = "host";
					status = "okay";
					vbus-supply = <&vdd_5v0_sys>;
				};

				usb3-0 {
					nvidia,usb2-companion = <2>;
					status = "okay";
				};

				usb3-1 {
					nvidia,usb2-companion = <0>;
					status = "okay";
				};

				usb3-2 {
					nvidia,usb2-companion = <1>;
					status = "okay";
				};
			};
		};

		usb@3550000 {
			status = "okay";

			phys = <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-0}>,
				<&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-1}>;
			phy-names = "usb2-0", "usb3-0";
		};

		usb@3610000 {
			status = "okay";

			phys =	<&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-0}>,
				<&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-1}>,
				<&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-2}>,
				<&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-3}>,
				<&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-0}>,
				<&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-1}>,
				<&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-2}>;
			phy-names = "usb2-0", "usb2-1", "usb2-2", "usb2-3",
				"usb3-0", "usb3-1", "usb3-2";
		};
	};

	vdd_3v3_dp: regulator-vdd-3v3-dp {
				compatible = "regulator-fixed";
				regulator-name = "VDD_3V3_DP";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				vin-supply = <&vdd_3v3_sys>;
				gpio = <&gpio TEGRA234_MAIN_GPIO(H, 6) 0>;
				enable-active-high;
				regulator-always-on;
	};

	vdd_3v3_sys: regulator-vdd-3v3-sys {
				compatible = "regulator-fixed";
				regulator-name = "VDD_3V3_SYS";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
	};

	vdd_3v3_wifi: regulator-vdd-3v3-wifi {
				compatible = "regulator-fixed";
				regulator-name = "VDD_3V3_WIFI";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				gpio = <&gpio TEGRA234_MAIN_GPIO(G, 3) GPIO_ACTIVE_HIGH>;
				regulator-boot-on;
				enable-active-high;
	};
};
