// SPDX-License-Identifier: GPL-2.0

#include "tegra234.dtsi"
#include "tegra234-p3701.dtsi"

/ {
	compatible = "nvidia,p3701-0008", "nvidia,tegra234";

	bus@0 {
		i2c@3160000 {
			status = "okay";

			eeprom@50 {
				compatible = "atmel,24c02";
				reg = <0x50>;
				label = "module";
				vcc-supply = <&vdd_1v8_hs>;
				address-width = <8>;
				pagesize = <8>;
				size = <256>;
				read-only;
			};
		};

		spi@3270000 {
			status = "okay";

			flash@0 {
				compatible = "jedec,spi-nor";
				reg = <0>;
				spi-max-frequency = <102000000>;
				spi-tx-bus-width = <4>;
				spi-rx-bus-width = <4>;
			};
		};

		mmc@3460000 {
			status = "okay";
			bus-width = <8>;
			non-removable;
		};

		i2c@c240000 {
			status = "okay";
		};

		rtc@c2a0000 {
			status = "okay";
		};

		pmc@c360000 {
			nvidia,invert-interrupt;
		};
	};

	bpmp {
		i2c {
			status = "okay";

			thermal-sensor@4c {
				status = "okay";
				reg = <0x4c>;
				vcc-supply = <&vdd_1v8_ao>;
			};
		};

		thermal {
			status = "okay";
		};
	};

	vdd_1v8_ao: regulator-vdd-1v8-ao {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_AO";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_1v8_hs: regulator-vdd-1v8-hs {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_HS";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_1v8_ls: regulator-vdd-1v8-ls {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_LS";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_3v3_ao: regulator-vdd-3v3-ao {
		compatible = "regulator-fixed";
		regulator-name = "vdd-AO-3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	vdd_5v0_sys: regulator-vdd-5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "VIN_SYS_5V0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};
};
