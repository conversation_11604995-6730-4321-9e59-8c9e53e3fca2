// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2017-2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

/ {
	fragment-camera@0 {
		target-path = "/";
		__overlay__ {
			tegra-capture-vi {
				num-channels = <2>;
				ports {
					#address-cells = <1>;
					#size-cells = <0>;
					port@0 {
						reg = <0>;
						liimx274_vi_in0: endpoint {
							port-index = <0>;
							bus-width = <4>;
							remote-endpoint = <&liimx274_csi_out0>;
						};
					};
					port@1 {
						reg = <1>;
						liimx274_vi_in1: endpoint {
							port-index = <2>;
							bus-width = <4>;
							remote-endpoint = <&liimx274_csi_out1>;
						};
					};
				};
			};
		};
	};
	fragment-camera@1 {
		target-path = "/bus@0";
		__overlay__ {
			host1x@13e00000 {
				nvcsi@15a00000 {
					num-channels = <2>;
					#address-cells = <1>;
					#size-cells = <0>;
					channel@0 {
						reg = <0>;
						ports {
							#address-cells = <1>;
							#size-cells = <0>;
							port@0 {
								reg = <0>;
								liimx274_csi_in0: endpoint@0 {
									port-index = <0>;
									bus-width = <4>;
									remote-endpoint = <&liimx274_imx274_out0>;
								};
							};
							port@1 {
								reg = <1>;
								liimx274_csi_out0: endpoint@1 {
									remote-endpoint = <&liimx274_vi_in0>;
								};
							};
						};
					};
					channel@1 {
						reg = <1>;
						ports {
							#address-cells = <1>;
							#size-cells = <0>;
							port@0 {
								reg = <0>;
								liimx274_csi_in1: endpoint@2 {
									port-index = <2>;
									bus-width = <4>;
									remote-endpoint = <&liimx274_imx274_out1>;
								};
							};
							port@1 {
								reg = <1>;
								liimx274_csi_out1: endpoint@3 {
									remote-endpoint = <&liimx274_vi_in1>;
								};
							};
						};
					};
				};
			};
			i2c@3180000 {
				tca9546@70 {
					i2c@0 {
					imx274_a@1a {
						compatible = "sony,imx274";
						/* I2C device address */
						reg = <0x1a>;
						/* V4L2 device node location */
						devnode = "video0";
						/* Physical dimensions of sensor */
						physical_w = "3.674";
						physical_h = "2.738";
						sensor_model = "imx274";
						/* Define any required hw resources needed by driver */
						/* ie. clocks, io pins, power sources */
						avdd-reg = "vana";
						iovdd-reg = "vif";
						/* Defines number of frames to be dropped by driver internally after applying */
						/* sensor crop settings. Some sensors send corrupt frames after applying */
						/* crop co-ordinates */
						/*post_crop_frame_drop = "0";*/
						/* if true, delay gain setting by one frame to be in sync with exposure */
						delayed_gain = "true";
						has-eeprom;
						fuse_id_start_addr = <91>;
						/**
						* ==== Modes ====
						* A modeX node is required to support v4l2 driver
						* implementation with NVIDIA camera software stack
						*
						* == Signal properties ==
						*
						* phy_mode = "";
						* PHY mode used by the MIPI lanes for this device
						*
						* tegra_sinterface = "";
						* CSI Serial interface connected to tegra
						* Incase of virtual HW devices, use virtual
						* For SW emulated devices, use host
						*
						* pix_clk_hz = "";
						* Sensor pixel clock used for calculations like exposure and framerate
						*
						* readout_orientation = "0";
						* Based on camera module orientation.
						* Only change readout_orientation if you specifically
						* Program a different readout order for this mode
						*
						* == Image format Properties ==
						*
						* active_w = "";
						* Pixel active region width
						*
						* active_h = "";
						* Pixel active region height
						*
						* pixel_t = "";
						* The sensor readout pixel pattern
						*
						* line_length = "";
						* Pixel line length (width) for sensor mode.
						*
						* == Source Control Settings ==
						*
						* Gain factor used to convert fixed point integer to float
						* Gain range [min_gain/gain_factor, max_gain/gain_factor]
						* Gain step [step_gain/gain_factor is the smallest step that can be configured]
						* Default gain [Default gain to be initialized for the control.
						*     use min_gain_val as default for optimal results]
						* Framerate factor used to convert fixed point integer to float
						* Framerate range [min_framerate/framerate_factor, max_framerate/framerate_factor]
						* Framerate step [step_framerate/framerate_factor is the smallest step that can be configured]
						* Default Framerate [Default framerate to be initialized for the control.
						*     use max_framerate to get required performance]
						* Exposure factor used to convert fixed point integer to float
						* For convenience use 1 sec = 1000000us as conversion factor
						* Exposure range [min_exp_time/exposure_factor, max_exp_time/exposure_factor]
						* Exposure step [step_exp_time/exposure_factor is the smallest step that can be configured]
						* Default Exposure Time [Default exposure to be initialized for the control.
						*     Set default exposure based on the default_framerate for optimal exposure settings]

						* For convenience use 1 sec = 1000000us as conversion factor
						*
						* gain_factor = ""; (integer factor used for floating to fixed point conversion)
						* min_gain_val = ""; (ceil to integer)
						* max_gain_val = ""; (ceil to integer)
						* step_gain_val = ""; (ceil to integer)
						* default_gain = ""; (ceil to integer)
						* Gain limits for mode
						*
						* exposure_factor = ""; (integer factor used for floating to fixed point conversion)
						* min_exp_time = ""; (ceil to integer)
						* max_exp_time = ""; (ceil to integer)
						* step_exp_time = ""; (ceil to integer)
						* default_exp_time = ""; (ceil to integer)
						* Exposure Time limits for mode (sec)
						*
						* framerate_factor = ""; (integer factor used for floating to fixed point conversion)
						* min_framerate = ""; (ceil to integer)
						* max_framerate = ""; (ceil to integer)
						* step_framerate = ""; (ceil to integer)
						* default_framerate = ""; (ceil to integer)
						* Framerate limits for mode (fps)
						*
						* embedded_metadata_height = "";
						* Sensor embedded metadata height in units of rows.
						* If sensor does not support embedded metadata value should be 0.
						* num_of_exposure = "";
						* Digital overlap(Dol) frames
						*
						* num_of_ignored_lines = "";
						* Used for cropping, eg. OB lines + Ignored area of effective pixel lines
						*
						* num_of_lines_offset_0 = "";
						* Used for cropping, vertical blanking in front of short exposure data
						* If more Dol frames are used, it can be extended, eg. num_of_lines_offset_1
						*
						* num_of_ignored_pixels = "";
						* Used for cropping, The length of line info(pixels)
						*
						* num_of_left_margin_pixels = "";
						* Used for cropping, the size of the left edge margin before
						* the active pixel area (after ignored pixels)
						*
						* num_of_right_margin_pixels = "";
						* Used for cropping, the size of the right edge margin after
						* the active pixel area
						*
						*/
						mode0 { // IMX274_MODE_3840X2160
							mclk_khz = "24000";
							num_lanes = "4";
							tegra_sinterface = "serial_a";
							phy_mode = "DPHY";
							discontinuous_clk = "yes";
							dpcm_enable = "false";
							cil_settletime = "0";
							active_w = "3840";
							active_h = "2160";
							mode_type = "bayer";
							pixel_phase = "rggb";
							csi_pixel_bit_depth = "10";
							readout_orientation = "0";
							line_length = "4208";
							inherent_gain = "1";
							mclk_multiplier = "24";
							pix_clk_hz = "576000000";
							gain_factor = "1000000";
							min_gain_val = "1000000";
							max_gain_val = "44400000";
							step_gain_val = "1";
							default_gain = "1000000";
							min_hdr_ratio = "1";
							max_hdr_ratio = "1";
							framerate_factor = "1000000";
							min_framerate = "1500000";
							max_framerate = "60000000";
							step_framerate = "1";
							default_framerate= "60000000";
							exposure_factor = "1000000";
							min_exp_time = "44";
							max_exp_time = "478696";
							step_exp_time = "1";
							default_exp_time = "16667";/* us */
							embedded_metadata_height = "1";
						};
						mode1 { // IMX274_MODE_1920X1080
							mclk_khz = "24000";
							num_lanes = "4";
							tegra_sinterface = "serial_a";
							phy_mode = "DPHY";
							discontinuous_clk = "yes";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer";
							pixel_phase = "rggb";
							active_w = "1920";
							active_h = "1080";
							readout_orientation = "0";
							line_length = "4160";
							inherent_gain = "1";
							mclk_multiplier = "24";
							pix_clk_hz = "576000000";
							gain_factor = "1000000";
							min_gain_val = "1000000";
							max_gain_val = "177000000";
							step_gain_val = "1";
							default_gain = "1000000";
							min_hdr_ratio = "1";
							max_hdr_ratio = "1";
							framerate_factor = "1000000";
							min_framerate = "1500000";
							max_framerate = "60000000";
							step_framerate = "1";
							default_framerate= "60000000";
							exposure_factor = "1000000";
							min_exp_time = "58";
							max_exp_time = "184611";
							step_exp_time = "1";
							default_exp_time = "16667";/* us */
							embedded_metadata_height = "1";
						};
						mode2 { // IMX274_MODE_3840X2160_DOL_30FPS
							mclk_khz = "24000";
							num_lanes = "4";
							tegra_sinterface = "serial_a";
							phy_mode = "DPHY";
							discontinuous_clk = "yes";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer_wdr_dol";
							pixel_phase = "rggb";
							active_w = "3856";
							active_h = "4448";
							readout_orientation = "0";
							line_length = "4208";
							inherent_gain = "1";
							mclk_multiplier = "24";
							pix_clk_hz = "576000000";
							gain_factor = "1000000";
							min_gain_val = "1000000";
							max_gain_val = "30000000";
							step_gain_val = "1";
							default_gain = "1000000";
							min_hdr_ratio = "32";
							max_hdr_ratio = "32";
							framerate_factor = "1000000";
							min_framerate = "1500000";
							max_framerate = "30000000";
							step_framerate = "1";
							default_framerate= "30000000";
							exposure_factor = "1000000";
							min_exp_time = "864";
							max_exp_time = "20480";
							step_exp_time = "1";
							default_exp_time = "20480";/* us */
							embedded_metadata_height = "1";
							num_of_exposure = "2";
							num_of_ignored_lines = "14";
							num_of_lines_offset_0 = "50";
							num_of_ignored_pixels = "4";
							num_of_left_margin_pixels = "12";
							num_of_right_margin_pixels = "0";
						};
						mode3 { // IMX274_MODE_1920X1080_DOL_60FPS
							mclk_khz = "24000";
							num_lanes = "4";
							tegra_sinterface = "serial_a";
							phy_mode = "DPHY";
							discontinuous_clk = "yes";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer_wdr_dol";
							pixel_phase = "rggb";
							active_w = "1936";
							active_h = "2264";
							readout_orientation = "0";
							line_length = "4160";
							inherent_gain = "1";
							mclk_multiplier = "24";
							pix_clk_hz = "576000000";
							gain_factor = "1000000";
							min_gain_val = "1000000";
							max_gain_val = "177000000";
							step_gain_val = "1";
							default_gain = "1000000";
							min_hdr_ratio = "32";
							max_hdr_ratio = "32";
							framerate_factor = "1000000";
							min_framerate = "1500000";
							max_framerate = "60000000";
							step_framerate = "1";
							default_framerate= "60000000";
							exposure_factor = "1000000";
							min_exp_time = "859";
							max_exp_time = "15649";
							step_exp_time = "1";
							default_exp_time = "15649";/* us */
							embedded_metadata_height = "1";
							/* WDR related settings */
							num_of_exposure = "2";
							num_of_ignored_lines = "14";
							num_of_lines_offset_0 = "38";
							num_of_ignored_pixels = "4";
							num_of_left_margin_pixels = "6";
							num_of_right_margin_pixels = "6";
						};
						ports {
							#address-cells = <1>;
							#size-cells = <0>;
							port@0 {
								reg = <0>;
								liimx274_imx274_out0: endpoint {
									port-index = <0>;
									bus-width = <4>;
									remote-endpoint = <&liimx274_csi_in0>;
									};
								};
							};
						};
					};
					i2c@1 {
					imx274_c@1a {
						compatible = "sony,imx274";
						/* I2C device address */
						reg = <0x1a>;
						/* V4L2 device node location */
						devnode = "video1";
						/* Physical dimensions of sensor */
						physical_w = "3.674";
						physical_h = "2.738";
						sensor_model = "imx274";
						/* Define any required hw resources needed by driver */
						/* ie. clocks, io pins, power sources */
						avdd-reg = "vana";
						iovdd-reg = "vif";
						/* Defines number of frames to be dropped by driver internally after applying */
						/* sensor crop settings. Some sensors send corrupt frames after applying */
						/* crop co-ordinates */
						/*post_crop_frame_drop = "0";*/
						/* if true, delay gain setting by one frame to be in sync with exposure */
						delayed_gain = "true";
						has-eeprom;
						fuse_id_start_addr = <99>;
						/**
						* ==== Modes ====
						* A modeX node is required to support v4l2 driver
						* implementation with NVIDIA camera software stack
						*
						* == Signal properties ==
						*
						* phy_mode = "";
						* PHY mode used by the MIPI lanes for this device
						*
						* tegra_sinterface = "";
						* CSI Serial interface connected to tegra
						* Incase of virtual HW devices, use virtual
						* For SW emulated devices, use host
						*
						* pix_clk_hz = "";
						* Sensor pixel clock used for calculations like exposure and framerate
						*
						* readout_orientation = "0";
						* Based on camera module orientation.
						* Only change readout_orientation if you specifically
						* Program a different readout order for this mode
						*
						* == Image format Properties ==
						*
						* active_w = "";
						* Pixel active region width
						*
						* active_h = "";
						* Pixel active region height
						*
						* pixel_t = "";
						* The sensor readout pixel pattern
						*
						* line_length = "";
						* Pixel line length (width) for sensor mode.
						*
						* == Source Control Settings ==
						*
						* Gain factor used to convert fixed point integer to float
						* Gain range [min_gain/gain_factor, max_gain/gain_factor]
						* Gain step [step_gain/gain_factor is the smallest step that can be configured]
						* Default gain [Default gain to be initialized for the control.
						*     use min_gain_val as default for optimal results]
						* Framerate factor used to convert fixed point integer to float
						* Framerate range [min_framerate/framerate_factor, max_framerate/framerate_factor]
						* Framerate step [step_framerate/framerate_factor is the smallest step that can be configured]
						* Default Framerate [Default framerate to be initialized for the control.
						*     use max_framerate to get required performance]
						* Exposure factor used to convert fixed point integer to float
						* For convenience use 1 sec = 1000000us as conversion factor
						* Exposure range [min_exp_time/exposure_factor, max_exp_time/exposure_factor]
						* Exposure step [step_exp_time/exposure_factor is the smallest step that can be configured]
						* Default Exposure Time [Default exposure to be initialized for the control.
						*     Set default exposure based on the default_framerate for optimal exposure settings]

						* For convenience use 1 sec = 1000000us as conversion factor
						*
						* gain_factor = ""; (integer factor used for floating to fixed point conversion)
						* min_gain_val = ""; (ceil to integer)
						* max_gain_val = ""; (ceil to integer)
						* step_gain_val = ""; (ceil to integer)
						* default_gain = ""; (ceil to integer)
						* Gain limits for mode
						*
						* exposure_factor = ""; (integer factor used for floating to fixed point conversion)
						* min_exp_time = ""; (ceil to integer)
						* max_exp_time = ""; (ceil to integer)
						* step_exp_time = ""; (ceil to integer)
						* default_exp_time = ""; (ceil to integer)
						* Exposure Time limits for mode (sec)
						*
						* framerate_factor = ""; (integer factor used for floating to fixed point conversion)
						* min_framerate = ""; (ceil to integer)
						* max_framerate = ""; (ceil to integer)
						* step_framerate = ""; (ceil to integer)
						* default_framerate = ""; (ceil to integer)
						* Framerate limits for mode (fps)
						*
						* embedded_metadata_height = "";
						* Sensor embedded metadata height in units of rows.
						* If sensor does not support embedded metadata value should be 0.
						* num_of_exposure = "";
						* Digital overlap(Dol) frames
						*
						* num_of_ignored_lines = "";
						* Used for cropping, eg. OB lines + Ignored area of effective pixel lines
						*
						* num_of_lines_offset_0 = "";
						* Used for cropping, vertical blanking in front of short exposure data
						* If more Dol frames are used, it can be extended, eg. num_of_lines_offset_1
						*
						* num_of_ignored_pixels = "";
						* Used for cropping, The length of line info(pixels)
						*
						* num_of_left_margin_pixels = "";
						* Used for cropping, the size of the left edge margin before
						* the active pixel area (after ignored pixels)
						*
						* num_of_right_margin_pixels = "";
						* Used for cropping, the size of the right edge margin after
						* the active pixel area
						*
						*/
						mode0 { // IMX274_MODE_3840X2160
							mclk_khz = "24000";
							num_lanes = "4";
							tegra_sinterface = "serial_c";
							phy_mode = "DPHY";
							discontinuous_clk = "yes";
							dpcm_enable = "false";
							cil_settletime = "0";
							active_w = "3840";
							active_h = "2160";
							mode_type = "bayer";
							pixel_phase = "rggb";
							csi_pixel_bit_depth = "10";
							readout_orientation = "0";
							line_length = "4208";
							inherent_gain = "1";
							mclk_multiplier = "24";
							pix_clk_hz = "576000000";
							gain_factor = "1000000";
							min_gain_val = "1000000";
							max_gain_val = "44400000";
							step_gain_val = "1";
							default_gain = "1000000";
							min_hdr_ratio = "1";
							max_hdr_ratio = "1";
							framerate_factor = "1000000";
							min_framerate = "1500000";
							max_framerate = "60000000";
							step_framerate = "1";
							default_framerate= "60000000";
							exposure_factor = "1000000";
							min_exp_time = "44";
							max_exp_time = "478696";
							step_exp_time = "1";
							default_exp_time = "16667";/* us */
							embedded_metadata_height = "1";
						};
						mode1 { // IMX274_MODE_1920X1080
							mclk_khz = "24000";
							num_lanes = "4";
							tegra_sinterface = "serial_c";
							phy_mode = "DPHY";
							discontinuous_clk = "yes";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer";
							pixel_phase = "rggb";
							active_w = "1920";
							active_h = "1080";
							readout_orientation = "0";
							line_length = "4160";
							inherent_gain = "1";
							mclk_multiplier = "24";
							pix_clk_hz = "576000000";
							gain_factor = "1000000";
							min_gain_val = "1000000";
							max_gain_val = "177000000";
							step_gain_val = "1";
							default_gain = "1000000";
							min_hdr_ratio = "1";
							max_hdr_ratio = "1";
							framerate_factor = "1000000";
							min_framerate = "1500000";
							max_framerate = "60000000";
							step_framerate = "1";
							default_framerate= "60000000";
							exposure_factor = "1000000";
							min_exp_time = "58";
							max_exp_time = "184611";
							step_exp_time = "1";
							default_exp_time = "16667";/* us */
							embedded_metadata_height = "1";
						};
						mode2 { // IMX274_MODE_3840X2160_DOL_30FPS
							mclk_khz = "24000";
							num_lanes = "4";
							tegra_sinterface = "serial_c";
							phy_mode = "DPHY";
							discontinuous_clk = "yes";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer_wdr_dol";
							pixel_phase = "rggb";
							active_w = "3856";
							active_h = "4448";
							readout_orientation = "0";
							line_length = "4208";
							inherent_gain = "1";
							mclk_multiplier = "24";
							pix_clk_hz = "576000000";
							gain_factor = "1000000";
							min_gain_val = "1000000";
							max_gain_val = "30000000";
							step_gain_val = "1";
							default_gain = "1000000";
							min_hdr_ratio = "32";
							max_hdr_ratio = "32";
							framerate_factor = "1000000";
							min_framerate = "1500000";
							max_framerate = "30000000";
							step_framerate = "1";
							default_framerate= "30000000";
							exposure_factor = "1000000";
							min_exp_time = "864";
							max_exp_time = "20480";
							step_exp_time = "1";
							default_exp_time = "20480";/* us */
							embedded_metadata_height = "1";
							num_of_exposure = "2";
							num_of_ignored_lines = "14";
							num_of_lines_offset_0 = "50";
							num_of_ignored_pixels = "4";
							num_of_left_margin_pixels = "12";
							num_of_right_margin_pixels = "0";
						};
						mode3 { // IMX274_MODE_1920X1080_DOL_60FPS
							mclk_khz = "24000";
							num_lanes = "4";
							tegra_sinterface = "serial_c";
							phy_mode = "DPHY";
							discontinuous_clk = "yes";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer_wdr_dol";
							pixel_phase = "rggb";
							active_w = "1936";
							active_h = "2264";
							readout_orientation = "0";
							line_length = "4160";
							inherent_gain = "1";
							mclk_multiplier = "24";
							pix_clk_hz = "576000000";
							gain_factor = "1000000";
							min_gain_val = "1000000";
							max_gain_val = "177000000";
							step_gain_val = "1";
							default_gain = "1000000";
							min_hdr_ratio = "32";
							max_hdr_ratio = "32";
							framerate_factor = "1000000";
							min_framerate = "1500000";
							max_framerate = "60000000";
							step_framerate = "1";
							default_framerate= "60000000";
							exposure_factor = "1000000";
							min_exp_time = "859";
							max_exp_time = "15649";
							step_exp_time = "1";
							default_exp_time = "15649";/* us */
							embedded_metadata_height = "1";
							/* WDR related settings */
							num_of_exposure = "2";
							num_of_ignored_lines = "14";
							num_of_lines_offset_0 = "38";
							num_of_ignored_pixels = "4";
							num_of_left_margin_pixels = "6";
							num_of_right_margin_pixels = "6";
						};
						ports {
							#address-cells = <1>;
							#size-cells = <0>;
							port@0 {
								reg = <0>;
								liimx274_imx274_out1: endpoint {
									port-index = <2>;
									bus-width = <4>;
									remote-endpoint = <&liimx274_csi_in1>;
									};
								};
							};
						};
					};
				};
			};
			lens_imx274@A6V26 {
				min_focus_distance = "0.0";
				hyper_focal = "0.0";
				focal_length = "5.00";
				f_number = "2.0";
				aperture = "2.2";
			};
		};
	};
};
/ {
	fragment-camera@2 {
		target-path = "/";
		__overlay__ {
			tegra-camera-platform {
				compatible = "nvidia, tegra-camera-platform";
				/**
				* Physical settings to calculate max ISO BW
				*
				* num_csi_lanes = <>;
				* Total number of CSI lanes when all cameras are active
				*
				* max_lane_speed = <>;
				* Max lane speed in Kbit/s
				*
				* min_bits_per_pixel = <>;
				* Min bits per pixel
				*
				* vi_peak_byte_per_pixel = <>;
				* Max byte per pixel for the VI ISO case
				*
				* vi_bw_margin_pct = <>;
				* Vi bandwidth margin in percentage
				*
				* max_pixel_rate = <>;
				* Max pixel rate in Kpixel/s for the ISP ISO case
				*
				* isp_peak_byte_per_pixel = <>;
				* Max byte per pixel for the ISP ISO case
				*
				* isp_bw_margin_pct = <>;
				* Isp bandwidth margin in percentage
				*/
				num_csi_lanes = <8>;
				max_lane_speed = <1500000>;
				min_bits_per_pixel = <10>;
				vi_peak_byte_per_pixel = <2>;
				vi_bw_margin_pct = <25>;
				max_pixel_rate = <750000>;
				isp_peak_byte_per_pixel = <5>;
				isp_bw_margin_pct = <25>;
				/**
				* The general guideline for naming badge_info contains 3 parts, and is as follows,
				* The first part is the camera_board_id for the module; if the module is in a FFD
				* platform, then use the platform name for this part.
				* The second part contains the position of the module, ex. "rear" or "front".
				* The third part contains the last 6 characters of a part number which is found
				* in the module's specsheet from the vender.
				*/
				modules {
					module0 {
						badge = "imx274_bottom_A6V26";
						position = "bottom";
						orientation = "1";
						drivernode0 {
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_sensor";
							/* Declare the device-tree hierarchy to driver instance */
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@0/imx274_a@1a";
						};
						drivernode1 {
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_lens";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/lens_imx274@A6V26/";
						};
					};
					module1 {
						badge = "imx274_top_A6V26";
						position = "top";
						orientation = "1";
						drivernode0 {
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_sensor";
							/* Declare the device-tree hierarchy to driver instance */
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@1/imx274_c@1a";
						};
						drivernode1 {
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_lens";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/lens_imx274@A6V26/";
						};
					};
				};
			};
		};
	};
};
