// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

#include <dt-bindings/clock/tegra234-clock.h>
#include <dt-bindings/reset/tegra234-reset.h>
#include <dt-bindings/memory/tegra234-mc.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/gpio/tegra234-gpio.h>
#include <dt-bindings/power/tegra234-powergate.h>

/ {
	overlay-name = "Add nvidia,t234 MODS Overlay Support";
	compatible = "nvidia,tegra234";

	fragment-t234@0 {
		target-path = "/";
		__overlay__ {
			mods-simple-bus {
				compatible = "simple-bus";
				device_type = "mods-simple-bus";
				#address-cells = <1>;
				#size-cells = <0>;

				mods-clocks {
					compatible = "nvidia,mods-clocks";
					status = "disabled";
					clocks = <&bpmp TEGRA234_CLK_ACTMON>,
						<&bpmp TEGRA234_CLK_ADSP>,
						<&bpmp TEGRA234_CLK_ADSPNEON>,
						<&bpmp TEGRA234_CLK_AHUB>,
						<&bpmp TEGRA234_CLK_APB2APE>,
						<&bpmp TEGRA234_CLK_APE>,
						<&bpmp TEGRA234_CLK_AUD_MCLK>,
						<&bpmp TEGRA234_CLK_AXI_CBB>,
						<&bpmp TEGRA234_CLK_CAN1>,
						<&bpmp TEGRA234_CLK_CAN1_HOST>,
						<&bpmp TEGRA234_CLK_CAN2>,
						<&bpmp TEGRA234_CLK_CAN2_HOST>,
						<&bpmp TEGRA234_CLK_CLK_M>,
						<&bpmp TEGRA234_CLK_DMIC1>,
						<&bpmp TEGRA234_CLK_DMIC2>,
						<&bpmp TEGRA234_CLK_DMIC3>,
						<&bpmp TEGRA234_CLK_DMIC4>,
						<&bpmp TEGRA234_CLK_DPAUX>,
						<&bpmp TEGRA234_CLK_NVJPG1>,
						<&bpmp TEGRA234_CLK_ACLK>,
						<&bpmp TEGRA234_CLK_MSS_ENCRYPT>,
						<&bpmp TEGRA234_CLK_EQOS_RX_INPUT>,
						<&bpmp TEGRA234_CLK_AON_APB>,
						<&bpmp TEGRA234_CLK_AON_NIC>,
						<&bpmp TEGRA234_CLK_AON_CPU_NIC>,
						<&bpmp TEGRA234_CLK_PLLA1>,
						<&bpmp TEGRA234_CLK_DSPK1>,
						<&bpmp TEGRA234_CLK_DSPK2>,
						<&bpmp TEGRA234_CLK_EMC>,
						<&bpmp TEGRA234_CLK_EQOS_AXI>,
						<&bpmp TEGRA234_CLK_EQOS_PTP_REF>,
						<&bpmp TEGRA234_CLK_EQOS_RX>,
						<&bpmp TEGRA234_CLK_EQOS_TX>,
						<&bpmp TEGRA234_CLK_EXTPERIPH1>,
						<&bpmp TEGRA234_CLK_EXTPERIPH2>,
						<&bpmp TEGRA234_CLK_EXTPERIPH3>,
						<&bpmp TEGRA234_CLK_EXTPERIPH4>,
						<&bpmp TEGRA234_CLK_FUSE>,
						<&bpmp TEGRA234_CLK_GPC0CLK>,
						<&bpmp TEGRA234_CLK_GPU_PWR>,
						<&bpmp TEGRA234_CLK_HOST1X>,
						<&bpmp TEGRA234_CLK_XUSB_HS_HSICP>,
						<&bpmp TEGRA234_CLK_I2C1>,
						<&bpmp TEGRA234_CLK_I2C2>,
						<&bpmp TEGRA234_CLK_I2C3>,
						<&bpmp TEGRA234_CLK_I2C4>,
						<&bpmp TEGRA234_CLK_I2C6>,
						<&bpmp TEGRA234_CLK_I2C7>,
						<&bpmp TEGRA234_CLK_I2C8>,
						<&bpmp TEGRA234_CLK_I2C9>,
						<&bpmp TEGRA234_CLK_I2S1>,
						<&bpmp TEGRA234_CLK_I2S1_SYNC_INPUT>,
						<&bpmp TEGRA234_CLK_I2S2>,
						<&bpmp TEGRA234_CLK_I2S2_SYNC_INPUT>,
						<&bpmp TEGRA234_CLK_I2S3>,
						<&bpmp TEGRA234_CLK_I2S3_SYNC_INPUT>,
						<&bpmp TEGRA234_CLK_I2S4>,
						<&bpmp TEGRA234_CLK_I2S4_SYNC_INPUT>,
						<&bpmp TEGRA234_CLK_I2S5>,
						<&bpmp TEGRA234_CLK_I2S5_SYNC_INPUT>,
						<&bpmp TEGRA234_CLK_I2S6>,
						<&bpmp TEGRA234_CLK_I2S6_SYNC_INPUT>,
						<&bpmp TEGRA234_CLK_ISP>,
						<&bpmp TEGRA234_CLK_EQOS_RX_M>,
						<&bpmp TEGRA234_CLK_MAUD>,
						<&bpmp TEGRA234_CLK_MIPI_CAL>,
						<&bpmp TEGRA234_CLK_MPHY_CORE_PLL_FIXED>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_ANA>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_LS_BIT>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_SYMB>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_LS_3XBIT>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_SYMB>,
						<&bpmp TEGRA234_CLK_MPHY_L1_RX_ANA>,
						<&bpmp TEGRA234_CLK_MPHY_TX_1MHZ_REF>,
						<&bpmp TEGRA234_CLK_NVCSI>,
						<&bpmp TEGRA234_CLK_NVCSILP>,
						<&bpmp TEGRA234_CLK_NVDEC>,
						<&bpmp TEGRA234_CLK_HUB>,
						<&bpmp TEGRA234_CLK_DISP>,
						<&bpmp TEGRA234_CLK_NVDISPLAY_P0>,
						<&bpmp TEGRA234_CLK_NVDISPLAY_P1>,
						<&bpmp TEGRA234_CLK_DSC>,
						<&bpmp TEGRA234_CLK_NVENC>,
						<&bpmp TEGRA234_CLK_NVJPG>,
						<&bpmp TEGRA234_CLK_OSC>,
						<&bpmp TEGRA234_CLK_AON_TOUCH>,
						<&bpmp TEGRA234_CLK_PLLA>,
						<&bpmp TEGRA234_CLK_PLLAON>,
						<&bpmp TEGRA234_CLK_PLLE>,
						<&bpmp TEGRA234_CLK_PLLP>,
						<&bpmp TEGRA234_CLK_PLLP_OUT0>,
						<&bpmp TEGRA234_CLK_UTMIP_PLL>,
						<&bpmp TEGRA234_CLK_PLLA_OUT0>,
						<&bpmp TEGRA234_CLK_PWM1>,
						<&bpmp TEGRA234_CLK_PWM2>,
						<&bpmp TEGRA234_CLK_PWM3>,
						<&bpmp TEGRA234_CLK_PWM4>,
						<&bpmp TEGRA234_CLK_PWM5>,
						<&bpmp TEGRA234_CLK_PWM6>,
						<&bpmp TEGRA234_CLK_PWM7>,
						<&bpmp TEGRA234_CLK_PWM8>,
						<&bpmp TEGRA234_CLK_RCE_CPU_NIC>,
						<&bpmp TEGRA234_CLK_RCE_NIC>,
						<&bpmp TEGRA234_CLK_AON_I2C_SLOW>,
						<&bpmp TEGRA234_CLK_SCE_CPU_NIC>,
						<&bpmp TEGRA234_CLK_SCE_NIC>,
						<&bpmp TEGRA234_CLK_SDMMC1>,
						<&bpmp TEGRA234_CLK_UPHY_PLL3>,
						<&bpmp TEGRA234_CLK_SDMMC4>,
						<&bpmp TEGRA234_CLK_SE>,
						<&bpmp TEGRA234_CLK_SOR0_PLL_REF>,
						<&bpmp TEGRA234_CLK_SOR0_REF>,
						<&bpmp TEGRA234_CLK_SOR1_PLL_REF>,
						<&bpmp TEGRA234_CLK_PRE_SOR0_REF>,
						<&bpmp TEGRA234_CLK_SOR1_REF>,
						<&bpmp TEGRA234_CLK_PRE_SOR1_REF>,
						<&bpmp TEGRA234_CLK_SOR_SAFE>,
						<&bpmp TEGRA234_CLK_DMIC5>,
						<&bpmp TEGRA234_CLK_SPI1>,
						<&bpmp TEGRA234_CLK_SPI2>,
						<&bpmp TEGRA234_CLK_SPI3>,
						<&bpmp TEGRA234_CLK_I2C_SLOW>,
						<&bpmp TEGRA234_CLK_SYNC_DMIC1>,
						<&bpmp TEGRA234_CLK_SYNC_DMIC2>,
						<&bpmp TEGRA234_CLK_SYNC_DMIC3>,
						<&bpmp TEGRA234_CLK_SYNC_DMIC4>,
						<&bpmp TEGRA234_CLK_SYNC_DSPK1>,
						<&bpmp TEGRA234_CLK_SYNC_DSPK2>,
						<&bpmp TEGRA234_CLK_SYNC_I2S1>,
						<&bpmp TEGRA234_CLK_SYNC_I2S2>,
						<&bpmp TEGRA234_CLK_SYNC_I2S3>,
						<&bpmp TEGRA234_CLK_SYNC_I2S4>,
						<&bpmp TEGRA234_CLK_SYNC_I2S5>,
						<&bpmp TEGRA234_CLK_SYNC_I2S6>,
						<&bpmp TEGRA234_CLK_MPHY_FORCE_LS_MODE>,
						<&bpmp TEGRA234_CLK_TACH0>,
						<&bpmp TEGRA234_CLK_TSEC>,
						<&bpmp TEGRA234_CLK_TSEC_PKA>,
						<&bpmp TEGRA234_CLK_UARTA>,
						<&bpmp TEGRA234_CLK_UARTB>,
						<&bpmp TEGRA234_CLK_UARTC>,
						<&bpmp TEGRA234_CLK_UARTD>,
						<&bpmp TEGRA234_CLK_UARTE>,
						<&bpmp TEGRA234_CLK_UARTF>,
						<&bpmp TEGRA234_CLK_PEX1_C6_CORE>,
						<&bpmp TEGRA234_CLK_UART_FST_MIPI_CAL>,
						<&bpmp TEGRA234_CLK_UFSDEV_REF>,
						<&bpmp TEGRA234_CLK_UFSHC>,
						<&bpmp TEGRA234_CLK_USB2_TRK>,
						<&bpmp TEGRA234_CLK_VI>,
						<&bpmp TEGRA234_CLK_VIC>,
						<&bpmp TEGRA234_CLK_CSITE>,
						<&bpmp TEGRA234_CLK_IST>,
						<&bpmp TEGRA234_CLK_JTAG_INTFC_PRE_CG>,
						<&bpmp TEGRA234_CLK_PEX2_C7_CORE>,
						<&bpmp TEGRA234_CLK_PEX2_C8_CORE>,
						<&bpmp TEGRA234_CLK_PEX2_C9_CORE>,
						<&bpmp TEGRA234_CLK_DLA0_FALCON>,
						<&bpmp TEGRA234_CLK_DLA0_CORE>,
						<&bpmp TEGRA234_CLK_DLA1_FALCON>,
						<&bpmp TEGRA234_CLK_DLA1_CORE>,
						<&bpmp TEGRA234_CLK_SOR0>,
						<&bpmp TEGRA234_CLK_SOR1>,
						<&bpmp TEGRA234_CLK_SOR_PAD_INPUT>,
						<&bpmp TEGRA234_CLK_PRE_SF0>,
						<&bpmp TEGRA234_CLK_SF0>,
						<&bpmp TEGRA234_CLK_SF1>,
						<&bpmp TEGRA234_CLK_DSI_PAD_INPUT>,
						<&bpmp TEGRA234_CLK_PEX2_C10_CORE>,
						<&bpmp TEGRA234_CLK_UARTI>,
						<&bpmp TEGRA234_CLK_UARTJ>,
						<&bpmp TEGRA234_CLK_UARTH>,
						<&bpmp TEGRA234_CLK_FUSE_SERIAL>,
						<&bpmp TEGRA234_CLK_QSPI0_2X_PM>,
						<&bpmp TEGRA234_CLK_QSPI1_2X_PM>,
						<&bpmp TEGRA234_CLK_QSPI0_PM>,
						<&bpmp TEGRA234_CLK_QSPI1_PM>,
						<&bpmp TEGRA234_CLK_VI_CONST>,
						<&bpmp TEGRA234_CLK_NAFLL_BPMP>,
						<&bpmp TEGRA234_CLK_NAFLL_SCE>,
						<&bpmp TEGRA234_CLK_NAFLL_NVDEC>,
						<&bpmp TEGRA234_CLK_NAFLL_NVJPG>,
						<&bpmp TEGRA234_CLK_NAFLL_TSEC>,
						<&bpmp TEGRA234_CLK_NAFLL_VI>,
						<&bpmp TEGRA234_CLK_NAFLL_SE>,
						<&bpmp TEGRA234_CLK_NAFLL_NVENC>,
						<&bpmp TEGRA234_CLK_NAFLL_ISP>,
						<&bpmp TEGRA234_CLK_NAFLL_VIC>,
						<&bpmp TEGRA234_CLK_NAFLL_AXICBB>,
						<&bpmp TEGRA234_CLK_NAFLL_NVJPG1>,
						<&bpmp TEGRA234_CLK_NAFLL_PVA0_CORE>,
						<&bpmp TEGRA234_CLK_NAFLL_PVA0_VPS>,
						<&bpmp TEGRA234_CLK_DBGAPB>,
						<&bpmp TEGRA234_CLK_NAFLL_RCE>,
						<&bpmp TEGRA234_CLK_LA>,
						<&bpmp TEGRA234_CLK_PLLP_OUT_JTAG>,
						<&bpmp TEGRA234_CLK_SDMMC4_AXICIF>,
						<&bpmp TEGRA234_CLK_SDMMC_LEGACY_TM>,
						<&bpmp TEGRA234_CLK_PEX0_C0_CORE>,
						<&bpmp TEGRA234_CLK_PEX0_C1_CORE>,
						<&bpmp TEGRA234_CLK_PEX0_C2_CORE>,
						<&bpmp TEGRA234_CLK_PEX0_C3_CORE>,
						<&bpmp TEGRA234_CLK_PEX0_C4_CORE>,
						<&bpmp TEGRA234_CLK_PEX1_C5_CORE>,
						<&bpmp TEGRA234_CLK_PEX0_C0_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX0_C1_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX0_C2_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX0_C3_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX0_C4_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX1_C5_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX1_C6_CORE_M>,
						<&bpmp TEGRA234_CLK_GPC1CLK>,
						<&bpmp TEGRA234_CLK_PLLC4>,
						<&bpmp TEGRA234_CLK_PLLC4_OUT1>,
						<&bpmp TEGRA234_CLK_PLLC4_OUT2>,
						<&bpmp TEGRA234_CLK_PLLC4_MUXED>,
						<&bpmp TEGRA234_CLK_PLLC4_VCO_DIV2>,
						<&bpmp TEGRA234_CLK_PLLNVHS>,
						<&bpmp TEGRA234_CLK_PEX2_C7_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX2_C8_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX2_C9_CORE_M>,
						<&bpmp TEGRA234_CLK_PEX2_C10_CORE_M>,
						<&bpmp TEGRA234_CLK_MGBE0_RX_INPUT>,
						<&bpmp TEGRA234_CLK_MGBE1_RX_INPUT>,
						<&bpmp TEGRA234_CLK_MGBE2_RX_INPUT>,
						<&bpmp TEGRA234_CLK_MGBE3_RX_INPUT>,
						<&bpmp TEGRA234_CLK_PEX_SATA_USB_RX_BYP>,
						<&bpmp TEGRA234_CLK_PEX_USB_PAD_PLL0_MGMT>,
						<&bpmp TEGRA234_CLK_PEX_USB_PAD_PLL1_MGMT>,
						<&bpmp TEGRA234_CLK_PEX_USB_PAD_PLL2_MGMT>,
						<&bpmp TEGRA234_CLK_PEX_USB_PAD_PLL3_MGMT>,
						<&bpmp TEGRA234_CLK_NVHS_RX_BYP_REF>,
						<&bpmp TEGRA234_CLK_NVHS_PLL0_MGMT>,
						<&bpmp TEGRA234_CLK_XUSB_CORE_DEV>,
						<&bpmp TEGRA234_CLK_XUSB_CORE_MUX>,
						<&bpmp TEGRA234_CLK_XUSB_CORE_HOST>,
						<&bpmp TEGRA234_CLK_XUSB_CORE_SS>,
						<&bpmp TEGRA234_CLK_XUSB_FALCON>,
						<&bpmp TEGRA234_CLK_XUSB_FALCON_HOST>,
						<&bpmp TEGRA234_CLK_XUSB_FALCON_SS>,
						<&bpmp TEGRA234_CLK_XUSB_FS>,
						<&bpmp TEGRA234_CLK_XUSB_FS_HOST>,
						<&bpmp TEGRA234_CLK_XUSB_FS_DEV>,
						<&bpmp TEGRA234_CLK_XUSB_SS>,
						<&bpmp TEGRA234_CLK_XUSB_SS_DEV>,
						<&bpmp TEGRA234_CLK_XUSB_SS_SUPERSPEED>,
						<&bpmp TEGRA234_CLK_NAFLL_CLUSTER0>,
						<&bpmp TEGRA234_CLK_NAFLL_CLUSTER1>,
						<&bpmp TEGRA234_CLK_NAFLL_CLUSTER2>,
						<&bpmp TEGRA234_CLK_CAN1_CORE>,
						<&bpmp TEGRA234_CLK_CAN2_CORE>,
						<&bpmp TEGRA234_CLK_PLLA1_OUT1>,
						<&bpmp TEGRA234_CLK_PLLNVHS_HPS>,
						<&bpmp TEGRA234_CLK_PLLREFE_VCOOUT>,
						<&bpmp TEGRA234_CLK_CLK_32K>,
						<&bpmp TEGRA234_CLK_UTMIPLL_CLKOUT48>,
						<&bpmp TEGRA234_CLK_UTMIPLL_CLKOUT480>,
						<&bpmp TEGRA234_CLK_PLLNVCSI>,
						<&bpmp TEGRA234_CLK_PVA0_CPU_AXI>,
						<&bpmp TEGRA234_CLK_PVA0_VPS>,
						<&bpmp TEGRA234_CLK_NAFLL_DLA0_CORE>,
						<&bpmp TEGRA234_CLK_NAFLL_DLA0_FALCON>,
						<&bpmp TEGRA234_CLK_NAFLL_DLA1_CORE>,
						<&bpmp TEGRA234_CLK_NAFLL_DLA1_FALCON>,
						<&bpmp TEGRA234_CLK_AON_UART_FST_MIPI_CAL>,
						<&bpmp TEGRA234_CLK_GPUSYS>,
						<&bpmp TEGRA234_CLK_I2C5>,
						<&bpmp TEGRA234_CLK_FR_SE>,
						<&bpmp TEGRA234_CLK_BPMP_CPU_NIC>,
						<&bpmp TEGRA234_CLK_BPMP_CPU>,
						<&bpmp TEGRA234_CLK_TSC>,
						<&bpmp TEGRA234_CLK_EMCSA_MPLL>,
						<&bpmp TEGRA234_CLK_EMCSB_MPLL>,
						<&bpmp TEGRA234_CLK_EMCSC_MPLL>,
						<&bpmp TEGRA234_CLK_EMCSD_MPLL>,
						<&bpmp TEGRA234_CLK_PLLC>,
						<&bpmp TEGRA234_CLK_PLLC2>,
						<&bpmp TEGRA234_CLK_TSC_REF>,
						<&bpmp TEGRA234_CLK_FUSE_BURN>,
						<&bpmp TEGRA234_CLK_PLLGBE>,
						<&bpmp TEGRA234_CLK_PLLGBE_HPS>,
						<&bpmp TEGRA234_CLK_EMCSA_EMC>,
						<&bpmp TEGRA234_CLK_EMCSB_EMC>,
						<&bpmp TEGRA234_CLK_EMCSC_EMC>,
						<&bpmp TEGRA234_CLK_EMCSD_EMC>,
						<&bpmp TEGRA234_CLK_PLLE_HPS>,
						<&bpmp TEGRA234_CLK_PLLREFE_VCOOUT_GATED>,
						<&bpmp TEGRA234_CLK_PLLP_DIV17>,
						<&bpmp TEGRA234_CLK_SOC_THERM>,
						<&bpmp TEGRA234_CLK_TSENSE>,
						<&bpmp TEGRA234_CLK_FR_SEU1>,
						<&bpmp TEGRA234_CLK_NAFLL_OFA>,
						<&bpmp TEGRA234_CLK_OFA>,
						<&bpmp TEGRA234_CLK_NAFLL_SEU1>,
						<&bpmp TEGRA234_CLK_SEU1>,
						<&bpmp TEGRA234_CLK_SPI4>,
						<&bpmp TEGRA234_CLK_SPI5>,
						<&bpmp TEGRA234_CLK_DCE_CPU_NIC>,
						<&bpmp TEGRA234_CLK_DCE_NIC>,
						<&bpmp TEGRA234_CLK_NAFLL_DCE>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_ANA_M>,
						<&bpmp TEGRA234_CLK_MPHY_L1_RX_ANA_M>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_PRE_SYMB>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_LS_SYMB_DIV>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_2X_SYMB>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_HS_SYMB_DIV>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_LS_3XBIT_DIV>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_MUX_SYMB_DIV>,
						<&bpmp TEGRA234_CLK_MPHY_L0_TX_SYMB_M>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_LS_SYMB_DIV>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_HS_SYMB_DIV>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_LS_BIT_DIV>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_MUX_SYMB_DIV>,
						<&bpmp TEGRA234_CLK_MPHY_L0_RX_SYMB_M>,
						<&bpmp TEGRA234_CLK_MGBE0_RX_INPUT_M>,
						<&bpmp TEGRA234_CLK_MGBE1_RX_INPUT_M>,
						<&bpmp TEGRA234_CLK_MGBE2_RX_INPUT_M>,
						<&bpmp TEGRA234_CLK_MGBE3_RX_INPUT_M>,
						<&bpmp TEGRA234_CLK_MGBE0_RX_PCS_M>,
						<&bpmp TEGRA234_CLK_MGBE1_RX_PCS_M>,
						<&bpmp TEGRA234_CLK_MGBE2_RX_PCS_M>,
						<&bpmp TEGRA234_CLK_MGBE3_RX_PCS_M>,
						<&bpmp TEGRA234_CLK_TACH1>,
						<&bpmp TEGRA234_CLK_MGBES_APP>,
						<&bpmp TEGRA234_CLK_UPHY_GBE_PLL2_TX_REF>,
						<&bpmp TEGRA234_CLK_UPHY_GBE_PLL2_XDIG>,
						<&bpmp TEGRA234_CLK_MGBE0_RX_PCS_INPUT>,
						<&bpmp TEGRA234_CLK_MGBE1_RX_PCS_INPUT>,
						<&bpmp TEGRA234_CLK_MGBE2_RX_PCS_INPUT>,
						<&bpmp TEGRA234_CLK_MGBE3_RX_PCS_INPUT>,
						<&bpmp TEGRA234_CLK_MGBE0_RX_PCS>,
						<&bpmp TEGRA234_CLK_MGBE0_TX>,
						<&bpmp TEGRA234_CLK_MGBE0_TX_PCS>,
						<&bpmp TEGRA234_CLK_MGBE0_MAC_DIVIDER>,
						<&bpmp TEGRA234_CLK_MGBE0_MAC>,
						<&bpmp TEGRA234_CLK_MGBE0_MACSEC>,
						<&bpmp TEGRA234_CLK_MGBE0_EEE_PCS>,
						<&bpmp TEGRA234_CLK_MGBE0_APP>,
						<&bpmp TEGRA234_CLK_MGBE0_PTP_REF>,
						<&bpmp TEGRA234_CLK_MGBE1_RX_PCS>,
						<&bpmp TEGRA234_CLK_MGBE1_TX>,
						<&bpmp TEGRA234_CLK_MGBE1_TX_PCS>,
						<&bpmp TEGRA234_CLK_MGBE1_MAC_DIVIDER>,
						<&bpmp TEGRA234_CLK_MGBE1_MAC>,
						<&bpmp TEGRA234_CLK_MGBE1_MACSEC>,
						<&bpmp TEGRA234_CLK_MGBE1_EEE_PCS>,
						<&bpmp TEGRA234_CLK_MGBE1_APP>,
						<&bpmp TEGRA234_CLK_MGBE1_PTP_REF>,
						<&bpmp TEGRA234_CLK_MGBE2_RX_PCS>,
						<&bpmp TEGRA234_CLK_MGBE2_TX>,
						<&bpmp TEGRA234_CLK_MGBE2_TX_PCS>,
						<&bpmp TEGRA234_CLK_MGBE2_MAC_DIVIDER>,
						<&bpmp TEGRA234_CLK_MGBE2_MAC>,
						<&bpmp TEGRA234_CLK_MGBE2_MACSEC>,
						<&bpmp TEGRA234_CLK_MGBE2_EEE_PCS>,
						<&bpmp TEGRA234_CLK_MGBE2_APP>,
						<&bpmp TEGRA234_CLK_MGBE2_PTP_REF>,
						<&bpmp TEGRA234_CLK_MGBE3_RX_PCS>,
						<&bpmp TEGRA234_CLK_MGBE3_TX>,
						<&bpmp TEGRA234_CLK_MGBE3_TX_PCS>,
						<&bpmp TEGRA234_CLK_MGBE3_MAC_DIVIDER>,
						<&bpmp TEGRA234_CLK_MGBE3_MAC>,
						<&bpmp TEGRA234_CLK_MGBE3_MACSEC>,
						<&bpmp TEGRA234_CLK_MGBE3_EEE_PCS>,
						<&bpmp TEGRA234_CLK_MGBE3_APP>,
						<&bpmp TEGRA234_CLK_MGBE3_PTP_REF>,
						<&bpmp TEGRA234_CLK_GBE_RX_BYP_REF>,
						<&bpmp TEGRA234_CLK_GBE_PLL0_MGMT>,
						<&bpmp TEGRA234_CLK_GBE_PLL1_MGMT>,
						<&bpmp TEGRA234_CLK_GBE_PLL2_MGMT>,
						<&bpmp TEGRA234_CLK_EQOS_MACSEC_RX>,
						<&bpmp TEGRA234_CLK_EQOS_MACSEC_TX>,
						<&bpmp TEGRA234_CLK_EQOS_TX_DIVIDER>,
						<&bpmp TEGRA234_CLK_NVHS_PLL1_MGMT>,
						<&bpmp TEGRA234_CLK_EMCHUB>,
						<&bpmp TEGRA234_CLK_I2S7_SYNC_INPUT>,
						<&bpmp TEGRA234_CLK_SYNC_I2S7>,
						<&bpmp TEGRA234_CLK_I2S7>,
						<&bpmp TEGRA234_CLK_I2S7_PAD_M>,
						<&bpmp TEGRA234_CLK_I2S8_SYNC_INPUT>,
						<&bpmp TEGRA234_CLK_SYNC_I2S8>,
						<&bpmp TEGRA234_CLK_I2S8>,
						<&bpmp TEGRA234_CLK_I2S8_PAD_M>,
						<&bpmp TEGRA234_CLK_NAFLL_GPC0>,
						<&bpmp TEGRA234_CLK_NAFLL_GPC1>,
						<&bpmp TEGRA234_CLK_NAFLL_GPUSYS>,
						<&bpmp TEGRA234_CLK_NAFLL_DSU0>,
						<&bpmp TEGRA234_CLK_NAFLL_DSU1>,
						<&bpmp TEGRA234_CLK_NAFLL_DSU2>,
						<&bpmp TEGRA234_CLK_SCE_CPU>,
						<&bpmp TEGRA234_CLK_RCE_CPU>,
						<&bpmp TEGRA234_CLK_DCE_CPU>,
						<&bpmp TEGRA234_CLK_DSIPLL_VCO>,
						<&bpmp TEGRA234_CLK_DSIPLL_CLKOUTPN>,
						<&bpmp TEGRA234_CLK_DSIPLL_CLKOUTA>,
						<&bpmp TEGRA234_CLK_SPPLL0_VCO>,
						<&bpmp TEGRA234_CLK_SPPLL0_CLKOUTPN>,
						<&bpmp TEGRA234_CLK_SPPLL0_CLKOUTA>,
						<&bpmp TEGRA234_CLK_SPPLL0_CLKOUTB>,
						<&bpmp TEGRA234_CLK_SPPLL0_DIV10>,
						<&bpmp TEGRA234_CLK_SPPLL0_DIV25>,
						<&bpmp TEGRA234_CLK_SPPLL0_DIV27PN>,
						<&bpmp TEGRA234_CLK_SPPLL1_VCO>,
						<&bpmp TEGRA234_CLK_SPPLL1_CLKOUTPN>,
						<&bpmp TEGRA234_CLK_SPPLL1_DIV27PN>,
						<&bpmp TEGRA234_CLK_VPLL0_REF>,
						<&bpmp TEGRA234_CLK_VPLL0>,
						<&bpmp TEGRA234_CLK_VPLL1>,
						<&bpmp TEGRA234_CLK_NVDISPLAY_P0_REF>,
						<&bpmp TEGRA234_CLK_RG0>,
						<&bpmp TEGRA234_CLK_RG1>,
						<&bpmp TEGRA234_CLK_DISPPLL>,
						<&bpmp TEGRA234_CLK_DISPHUBPLL>,
						<&bpmp TEGRA234_CLK_DSI_LP>,
						<&bpmp TEGRA234_CLK_AZA_2XBIT>,
						<&bpmp TEGRA234_CLK_AZA_BIT>,
						<&bpmp TEGRA234_CLK_DSI_CORE>,
						<&bpmp TEGRA234_CLK_DSI_PIXEL>,
						<&bpmp TEGRA234_CLK_PRE_SOR0>,
						<&bpmp TEGRA234_CLK_PRE_SOR1>,
						<&bpmp TEGRA234_CLK_DP_LINK_REF>,
						<&bpmp TEGRA234_CLK_SOR_LINKA_INPUT>,
						<&bpmp TEGRA234_CLK_SOR_LINKA_AFIFO>,
						<&bpmp TEGRA234_CLK_SOR_LINKA_AFIFO_M>,
						<&bpmp TEGRA234_CLK_RG0_M>,
						<&bpmp TEGRA234_CLK_RG1_M>,
						<&bpmp TEGRA234_CLK_SOR0_M>,
						<&bpmp TEGRA234_CLK_SOR1_M>,
						<&bpmp TEGRA234_CLK_PLLHUB>,
						<&bpmp TEGRA234_CLK_MCHUB>,
						<&bpmp TEGRA234_CLK_EMCSA_MC>,
						<&bpmp TEGRA234_CLK_EMCSB_MC>,
						<&bpmp TEGRA234_CLK_EMCSC_MC>,
						<&bpmp TEGRA234_CLK_EMCSD_MC>;
					clock-names = "actmon",
						"adsp",
						"adspneon",
						"ahub",
						"apb2ape",
						"ape",
						"aud_mclk",
						"axi_cbb",
						"can1",
						"can1_host",
						"can2",
						"can2_host",
						"clk_m",
						"dmic1",
						"dmic2",
						"dmic3",
						"dmic4",
						"dpaux",
						"nvjpg1",
						"aclk",
						"mss_encrypt",
						"eqos_rx_input",
						"aon_apb",
						"aon_nic",
						"aon_cpu_nic",
						"plla1",
						"dspk1",
						"dspk2",
						"emc",
						"eqos_axi",
						"eqos_ptp_ref",
						"eqos_rx",
						"eqos_tx",
						"extperiph1",
						"extperiph2",
						"extperiph3",
						"extperiph4",
						"fuse",
						"gpc0clk",
						"gpu_pwr",
						"host1x",
						"xusb_hs_hsicp",
						"i2c1",
						"i2c2",
						"i2c3",
						"i2c4",
						"i2c6",
						"i2c7",
						"i2c8",
						"i2c9",
						"i2s1",
						"i2s1_sync_input",
						"i2s2",
						"i2s2_sync_input",
						"i2s3",
						"i2s3_sync_input",
						"i2s4",
						"i2s4_sync_input",
						"i2s5",
						"i2s5_sync_input",
						"i2s6",
						"i2s6_sync_input",
						"isp",
						"eqos_rx_m",
						"maud",
						"mipi_cal",
						"mphy_core_pll_fixed",
						"mphy_l0_rx_ana",
						"mphy_l0_rx_ls_bit",
						"mphy_l0_rx_symb",
						"mphy_l0_tx_ls_3xbit",
						"mphy_l0_tx_symb",
						"mphy_l1_rx_ana",
						"mphy_tx_1mhz_ref",
						"nvcsi",
						"nvcsilp",
						"nvdec",
						"hub",
						"disp",
						"nvdisplay_p0",
						"nvdisplay_p1",
						"dsc",
						"nvenc",
						"nvjpg",
						"osc",
						"aon_touch",
						"plla",
						"pllaon",
						"plle",
						"pllp",
						"pllp_out0",
						"utmip_pll",
						"plla_out0",
						"pwm1",
						"pwm2",
						"pwm3",
						"pwm4",
						"pwm5",
						"pwm6",
						"pwm7",
						"pwm8",
						"rce_cpu_nic",
						"rce_nic",
						"aon_i2c_slow",
						"sce_cpu_nic",
						"sce_nic",
						"sdmmc1",
						"uphy_pll3",
						"sdmmc4",
						"se",
						"sor0_pll_ref",
						"sor0_ref",
						"sor1_pll_ref",
						"pre_sor0_ref",
						"sor1_ref",
						"pre_sor1_ref",
						"sor_safe",
						"dmic5",
						"spi1",
						"spi2",
						"spi3",
						"i2c_slow",
						"sync_dmic1",
						"sync_dmic2",
						"sync_dmic3",
						"sync_dmic4",
						"sync_dspk1",
						"sync_dspk2",
						"sync_i2s1",
						"sync_i2s2",
						"sync_i2s3",
						"sync_i2s4",
						"sync_i2s5",
						"sync_i2s6",
						"mphy_force_ls_mode",
						"tach0",
						"tsec",
						"tsec_pka",
						"uarta",
						"uartb",
						"uartc",
						"uartd",
						"uarte",
						"uartf",
						"pex1_c6_core",
						"uart_fst_mipi_cal",
						"ufsdev_ref",
						"ufshc",
						"usb2_trk",
						"vi",
						"vic",
						"csite",
						"ist",
						"jtag_intfc_pre_cg",
						"pex2_c7_core",
						"pex2_c8_core",
						"pex2_c9_core",
						"dla0_falcon",
						"dla0_core",
						"dla1_falcon",
						"dla1_core",
						"sor0",
						"sor1",
						"sor_pad_input",
						"pre_sf0",
						"sf0",
						"sf1",
						"dsi_pad_input",
						"pex2_c10_core",
						"uarti",
						"uartj",
						"uarth",
						"fuse_serial",
						"qspi0_2x_pm",
						"qspi1_2x_pm",
						"qspi0_pm",
						"qspi1_pm",
						"vi_const",
						"nafll_bpmp",
						"nafll_sce",
						"nafll_nvdec",
						"nafll_nvjpg",
						"nafll_tsec",
						"nafll_vi",
						"nafll_se",
						"nafll_nvenc",
						"nafll_isp",
						"nafll_vic",
						"nafll_axicbb",
						"nafll_nvjpg1",
						"nafll_pva0_core",
						"nafll_pva0_vps",
						"dbgapb",
						"nafll_rce",
						"la",
						"pllp_out_jtag",
						"sdmmc4_axicif",
						"sdmmc_legacy_tm",
						"pex0_c0_core",
						"pex0_c1_core",
						"pex0_c2_core",
						"pex0_c3_core",
						"pex0_c4_core",
						"pex1_c5_core",
						"pex0_c0_core_m",
						"pex0_c1_core_m",
						"pex0_c2_core_m",
						"pex0_c3_core_m",
						"pex0_c4_core_m",
						"pex1_c5_core_m",
						"pex1_c6_core_m",
						"gpc1clk",
						"pllc4",
						"pllc4_out1",
						"pllc4_out2",
						"pllc4_muxed",
						"pllc4_vco_div2",
						"pllnvhs",
						"pex2_c7_core_m",
						"pex2_c8_core_m",
						"pex2_c9_core_m",
						"pex2_c10_core_m",
						"mgbe0_rx_input",
						"mgbe1_rx_input",
						"mgbe2_rx_input",
						"mgbe3_rx_input",
						"pex_sata_usb_rx_byp",
						"pex_usb_pad_pll0_mgmt",
						"pex_usb_pad_pll1_mgmt",
						"pex_usb_pad_pll2_mgmt",
						"pex_usb_pad_pll3_mgmt",
						"nvhs_rx_byp_ref",
						"nvhs_pll0_mgmt",
						"xusb_core_dev",
						"xusb_core_mux",
						"xusb_core_host",
						"xusb_core_ss",
						"xusb_falcon",
						"xusb_falcon_host",
						"xusb_falcon_ss",
						"xusb_fs",
						"xusb_fs_host",
						"xusb_fs_dev",
						"xusb_ss",
						"xusb_ss_dev",
						"xusb_ss_superspeed",
						"nafll_cluster0",
						"nafll_cluster1",
						"nafll_cluster2",
						"can1_core",
						"can2_core",
						"plla1_out1",
						"pllnvhs_hps",
						"pllrefe_vcoout",
						"clk_32k",
						"utmipll_clkout48",
						"utmipll_clkout480",
						"pllnvcsi",
						"pva0_cpu_axi",
						"pva0_vps",
						"nafll_dla0_core",
						"nafll_dla0_falcon",
						"nafll_dla1_core",
						"nafll_dla1_falcon",
						"aon_uart_fst_mipi_cal",
						"gpusys",
						"i2c5",
						"fr_se",
						"bpmp_cpu_nic",
						"bpmp_cpu",
						"tsc",
						"emcsa_mpll",
						"emcsb_mpll",
						"emcsc_mpll",
						"emcsd_mpll",
						"pllc",
						"pllc2",
						"tsc_ref",
						"fuse_burn",
						"pllgbe",
						"pllgbe_hps",
						"emcsa_emc",
						"emcsb_emc",
						"emcsc_emc",
						"emcsd_emc",
						"plle_hps",
						"pllrefe_vcoout_gated",
						"pllp_div17",
						"soc_therm",
						"tsense",
						"fr_seu1",
						"nafll_ofa",
						"ofa",
						"nafll_seu1",
						"seu1",
						"spi4",
						"spi5",
						"dce_cpu_nic",
						"dce_nic",
						"nafll_dce",
						"mphy_l0_rx_ana_m",
						"mphy_l1_rx_ana_m",
						"mphy_l0_tx_pre_symb",
						"mphy_l0_tx_ls_symb_div",
						"mphy_l0_tx_2x_symb",
						"mphy_l0_tx_hs_symb_div",
						"mphy_l0_tx_ls_3xbit_div",
						"mphy_l0_tx_mux_symb_div",
						"mphy_l0_tx_symb_m",
						"mphy_l0_rx_ls_symb_div",
						"mphy_l0_rx_hs_symb_div",
						"mphy_l0_rx_ls_bit_div",
						"mphy_l0_rx_mux_symb_div",
						"mphy_l0_rx_symb_m",
						"mgbe0_rx_input_m",
						"mgbe1_rx_input_m",
						"mgbe2_rx_input_m",
						"mgbe3_rx_input_m",
						"mgbe0_rx_pcs_m",
						"mgbe1_rx_pcs_m",
						"mgbe2_rx_pcs_m",
						"mgbe3_rx_pcs_m",
						"tach1",
						"mgbes_app",
						"uphy_gbe_pll2_tx_ref",
						"uphy_gbe_pll2_xdig",
						"mgbe0_rx_pcs_input",
						"mgbe1_rx_pcs_input",
						"mgbe2_rx_pcs_input",
						"mgbe3_rx_pcs_input",
						"mgbe0_rx_pcs",
						"mgbe0_tx",
						"mgbe0_tx_pcs",
						"mgbe0_mac_divider",
						"mgbe0_mac",
						"mgbe0_macsec",
						"mgbe0_eee_pcs",
						"mgbe0_app",
						"mgbe0_ptp_ref",
						"mgbe1_rx_pcs",
						"mgbe1_tx",
						"mgbe1_tx_pcs",
						"mgbe1_mac_divider",
						"mgbe1_mac",
						"mgbe1_macsec",
						"mgbe1_eee_pcs",
						"mgbe1_app",
						"mgbe1_ptp_ref",
						"mgbe2_rx_pcs",
						"mgbe2_tx",
						"mgbe2_tx_pcs",
						"mgbe2_mac_divider",
						"mgbe2_mac",
						"mgbe2_macsec",
						"mgbe2_eee_pcs",
						"mgbe2_app",
						"mgbe2_ptp_ref",
						"mgbe3_rx_pcs",
						"mgbe3_tx",
						"mgbe3_tx_pcs",
						"mgbe3_mac_divider",
						"mgbe3_mac",
						"mgbe3_macsec",
						"mgbe3_eee_pcs",
						"mgbe3_app",
						"mgbe3_ptp_ref",
						"gbe_rx_byp_ref",
						"gbe_pll0_mgmt",
						"gbe_pll1_mgmt",
						"gbe_pll2_mgmt",
						"eqos_macsec_rx",
						"eqos_macsec_tx",
						"eqos_tx_divider",
						"nvhs_pll1_mgmt",
						"emchub",
						"i2s7_sync_input",
						"sync_i2s7",
						"i2s7",
						"i2s7_pad_m",
						"i2s8_sync_input",
						"sync_i2s8",
						"i2s8",
						"i2s8_pad_m",
						"nafll_gpc0",
						"nafll_gpc1",
						"nafll_gpusys",
						"nafll_dsu0",
						"nafll_dsu1",
						"nafll_dsu2",
						"sce_cpu",
						"rce_cpu",
						"dce_cpu",
						"dsipll_vco",
						"dsipll_clkoutpn",
						"dsipll_clkouta",
						"sppll0_vco",
						"sppll0_clkoutpn",
						"sppll0_clkouta",
						"sppll0_clkoutb",
						"sppll0_div10",
						"sppll0_div25",
						"sppll0_div27pn",
						"sppll1_vco",
						"sppll1_clkoutpn",
						"sppll1_div27pn",
						"vpll0_ref",
						"vpll0",
						"vpll1",
						"nvdisplay_p0_ref",
						"rg0",
						"rg1",
						"disppll",
						"disphubpll",
						"dsi_lp",
						"aza_2xbit",
						"aza_bit",
						"dsi_core",
						"dsi_pixel",
						"pre_sor0",
						"pre_sor1",
						"dp_link_ref",
						"sor_linka_input",
						"sor_linka_afifo",
						"sor_linka_afifo_m",
						"rg0_m",
						"rg1_m",
						"sor0_m",
						"sor1_m",
						"pllhub",
						"mchub",
						"emcsa_mc",
						"emcsb_mc",
						"emcsc_mc",
						"emcsd_mc";
					resets = <&bpmp TEGRA234_RESET_ACTMON>,
						 <&bpmp TEGRA234_RESET_ADSP_ALL>,
						 <&bpmp TEGRA234_RESET_CAN1>,
						 <&bpmp TEGRA234_RESET_CAN2>,
						 <&bpmp TEGRA234_RESET_DLA0>,
						 <&bpmp TEGRA234_RESET_DLA1>,
						 <&bpmp TEGRA234_RESET_DPAUX>,
						 <&bpmp TEGRA234_RESET_OFA>,
						 <&bpmp TEGRA234_RESET_NVJPG1>,
						 <&bpmp TEGRA234_RESET_PEX1_CORE_6>,
						 <&bpmp TEGRA234_RESET_PEX1_CORE_6_APB>,
						 <&bpmp TEGRA234_RESET_PEX1_COMMON_APB>,
						 <&bpmp TEGRA234_RESET_PEX2_CORE_7>,
						 <&bpmp TEGRA234_RESET_PEX2_CORE_7_APB>,
						 <&bpmp TEGRA234_RESET_NVDISPLAY>,
						 <&bpmp TEGRA234_RESET_EQOS>,
						 <&bpmp TEGRA234_RESET_GPCDMA>,
						 <&bpmp TEGRA234_RESET_GPU>,
						 <&bpmp TEGRA234_RESET_HDA>,
						 <&bpmp TEGRA234_RESET_HDACODEC>,
						 <&bpmp TEGRA234_RESET_EQOS_MACSEC>,
						 <&bpmp TEGRA234_RESET_EQOS_MACSEC_SECURE>,
						 <&bpmp TEGRA234_RESET_I2C1>,
						 <&bpmp TEGRA234_RESET_PEX2_CORE_8>,
						 <&bpmp TEGRA234_RESET_PEX2_CORE_8_APB>,
						 <&bpmp TEGRA234_RESET_PEX2_CORE_9>,
						 <&bpmp TEGRA234_RESET_PEX2_CORE_9_APB>,
						 <&bpmp TEGRA234_RESET_I2C2>,
						 <&bpmp TEGRA234_RESET_I2C3>,
						 <&bpmp TEGRA234_RESET_I2C4>,
						 <&bpmp TEGRA234_RESET_I2C6>,
						 <&bpmp TEGRA234_RESET_I2C7>,
						 <&bpmp TEGRA234_RESET_I2C8>,
						 <&bpmp TEGRA234_RESET_I2C9>,
						 <&bpmp TEGRA234_RESET_ISP>,
						 <&bpmp TEGRA234_RESET_MIPI_CAL>,
						 <&bpmp TEGRA234_RESET_MPHY_CLK_CTL>,
						 <&bpmp TEGRA234_RESET_MPHY_L0_RX>,
						 <&bpmp TEGRA234_RESET_MPHY_L0_TX>,
						 <&bpmp TEGRA234_RESET_MPHY_L1_RX>,
						 <&bpmp TEGRA234_RESET_MPHY_L1_TX>,
						 <&bpmp TEGRA234_RESET_NVCSI>,
						 <&bpmp TEGRA234_RESET_NVDEC>,
						 <&bpmp TEGRA234_RESET_MGBE0_PCS>,
						 <&bpmp TEGRA234_RESET_MGBE0_MAC>,
						 <&bpmp TEGRA234_RESET_MGBE0_MACSEC>,
						 <&bpmp TEGRA234_RESET_MGBE0_MACSEC_SECURE>,
						 <&bpmp TEGRA234_RESET_MGBE1_PCS>,
						 <&bpmp TEGRA234_RESET_MGBE1_MAC>,
						 <&bpmp TEGRA234_RESET_MGBE1_MACSEC>,
						 <&bpmp TEGRA234_RESET_MGBE1_MACSEC_SECURE>,
						 <&bpmp TEGRA234_RESET_MGBE2_PCS>,
						 <&bpmp TEGRA234_RESET_MGBE2_MAC>,
						 <&bpmp TEGRA234_RESET_MGBE2_MACSEC>,
						 <&bpmp TEGRA234_RESET_PEX2_CORE_10>,
						 <&bpmp TEGRA234_RESET_PEX2_CORE_10_APB>,
						 <&bpmp TEGRA234_RESET_PEX2_COMMON_APB>,
						 <&bpmp TEGRA234_RESET_NVENC>,
						 <&bpmp TEGRA234_RESET_MGBE2_MACSEC_SECURE>,
						 <&bpmp TEGRA234_RESET_NVJPG>,
						 <&bpmp TEGRA234_RESET_LA>,
						 <&bpmp TEGRA234_RESET_HWPM>,
						 <&bpmp TEGRA234_RESET_PVA0_ALL>,
						 <&bpmp TEGRA234_RESET_PWM1>,
						 <&bpmp TEGRA234_RESET_PWM2>,
						 <&bpmp TEGRA234_RESET_PWM3>,
						 <&bpmp TEGRA234_RESET_PWM4>,
						 <&bpmp TEGRA234_RESET_PWM5>,
						 <&bpmp TEGRA234_RESET_PWM6>,
						 <&bpmp TEGRA234_RESET_PWM7>,
						 <&bpmp TEGRA234_RESET_PWM8>,
						 <&bpmp TEGRA234_RESET_QSPI0>,
						 <&bpmp TEGRA234_RESET_QSPI1>,
						 <&bpmp TEGRA234_RESET_SCE_ALL>,
						 <&bpmp TEGRA234_RESET_RCE_ALL>,
						 <&bpmp TEGRA234_RESET_SDMMC1>,
						 <&bpmp TEGRA234_RESET_SDMMC4>,
						 <&bpmp TEGRA234_RESET_MGBE3_PCS>,
						 <&bpmp TEGRA234_RESET_MGBE3_MAC>,
						 <&bpmp TEGRA234_RESET_MGBE3_MACSEC>,
						 <&bpmp TEGRA234_RESET_MGBE3_MACSEC_SECURE>,
						 <&bpmp TEGRA234_RESET_SPI1>,
						 <&bpmp TEGRA234_RESET_SPI2>,
						 <&bpmp TEGRA234_RESET_SPI3>,
						 <&bpmp TEGRA234_RESET_SPI4>,
						 <&bpmp TEGRA234_RESET_TACH0>,
						 <&bpmp TEGRA234_RESET_TACH1>,
						 <&bpmp TEGRA234_RESET_TSEC>,
						 <&bpmp TEGRA234_RESET_UARTA>,
						 <&bpmp TEGRA234_RESET_UARTB>,
						 <&bpmp TEGRA234_RESET_UARTC>,
						 <&bpmp TEGRA234_RESET_UARTD>,
						 <&bpmp TEGRA234_RESET_UARTE>,
						 <&bpmp TEGRA234_RESET_UARTF>,
						 <&bpmp TEGRA234_RESET_UARTH>,
						 <&bpmp TEGRA234_RESET_UFSHC>,
						 <&bpmp TEGRA234_RESET_UFSHC_AXI_M>,
						 <&bpmp TEGRA234_RESET_UFSHC_LP_SEQ>,
						 <&bpmp TEGRA234_RESET_VI>,
						 <&bpmp TEGRA234_RESET_VIC>,
						 <&bpmp TEGRA234_RESET_XUSB_PADCTL>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_0>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_1>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_2>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_3>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_4>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_0_APB>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_1_APB>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_2_APB>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_3_APB>,
						 <&bpmp TEGRA234_RESET_PEX0_CORE_4_APB>,
						 <&bpmp TEGRA234_RESET_PEX0_COMMON_APB>,
						 <&bpmp TEGRA234_RESET_PEX1_CORE_5>,
						 <&bpmp TEGRA234_RESET_PEX1_CORE_5_APB>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_PM>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_PLL0>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_L0>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_L1>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_L2>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_L3>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_L4>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_L5>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_L6>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_L7>,
						 <&bpmp TEGRA234_RESET_NVHS_UPHY_PM>,
						 <&bpmp TEGRA234_RESET_DMIC5>,
						 <&bpmp TEGRA234_RESET_APE>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_L0>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_L1>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_L2>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_L3>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_L4>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_L5>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_L6>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_L7>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_PLL0>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_PLL1>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_PLL2>,
						 <&bpmp TEGRA234_RESET_PEX_USB_UPHY_PLL3>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_L0>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_L1>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_L2>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_L3>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_L4>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_L5>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_L6>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_L7>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_PLL0>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_PLL1>,
						 <&bpmp TEGRA234_RESET_GBE_UPHY_PLL2>;
					reset-names = "actmon",
						       "adsp_all",
						       "can1",
						       "can2",
						       "dla0",
						       "dla1",
						       "dpaux",
						       "ofa",
						       "nvjpg1",
						       "pex1_core_6",
						       "pex1_core_6_apb",
						       "pex1_common_apb",
						       "pex2_core_7",
						       "pex2_core_7_apb",
						       "nvdisplay",
						       "eqos",
						       "gpcdma",
						       "gpu",
						       "hda",
						       "hdacodec",
						       "eqos_macsec",
						       "eqos_macsec_secure",
						       "i2c1",
						       "pex2_core_8",
						       "pex2_core_8_apb",
						       "pex2_core_9",
						       "pex2_core_9_apb",
						       "i2c2",
						       "i2c3",
						       "i2c4",
						       "i2c6",
						       "i2c7",
						       "i2c8",
						       "i2c9",
						       "isp",
						       "mipi_cal",
						       "mphy_clk_ctl",
						       "mphy_l0_rx",
						       "mphy_l0_tx",
						       "mphy_l1_rx",
						       "mphy_l1_tx",
						       "nvcsi",
						       "nvdec",
						       "mgbe0_pcs",
						       "mgbe0_mac",
						       "mgbe0_macsec",
						       "mgbe0_macsec_secure",
						       "mgbe1_pcs",
						       "mgbe1_mac",
						       "mgbe1_macsec",
						       "mgbe1_macsec_secure",
						       "mgbe2_pcs",
						       "mgbe2_mac",
						       "mgbe2_macsec",
						       "pex2_core_10",
						       "pex2_core_10_apb",
						       "pex2_common_apb",
						       "nvenc",
						       "mgbe2_macsec_secure",
						       "nvjpg",
						       "la",
						       "hwpm",
						       "pva0_all",
						       "pwm1",
						       "pwm2",
						       "pwm3",
						       "pwm4",
						       "pwm5",
						       "pwm6",
						       "pwm7",
						       "pwm8",
						       "qspi0",
						       "qspi1",
						       "sce_all",
						       "rce_all",
						       "sdmmc1",
						       "sdmmc4",
						       "mgbe3_pcs",
						       "mgbe3_mac",
						       "mgbe3_macsec",
						       "mgbe3_macsec_secure",
						       "spi1",
						       "spi2",
						       "spi3",
						       "spi4",
						       "tach0",
						       "tach1",
						       "tsec",
						       "uarta",
						       "uartb",
						       "uartc",
						       "uartd",
						       "uarte",
						       "uartf",
						       "uarth",
						       "ufshc",
						       "ufshc_axi_m",
						       "ufshc_lp_seq",
						       "vi",
						       "vic",
						       "xusb_padctl",
						       "pex0_core_0",
						       "pex0_core_1",
						       "pex0_core_2",
						       "pex0_core_3",
						       "pex0_core_4",
						       "pex0_core_0_apb",
						       "pex0_core_1_apb",
						       "pex0_core_2_apb",
						       "pex0_core_3_apb",
						       "pex0_core_4_apb",
						       "pex0_common_apb",
						       "pex1_core_5",
						       "pex1_core_5_apb",
						       "gbe_uphy",
						       "gbe_uphy_pm",
						       "nvhs_uphy",
						       "nvhs_uphy_pll0",
						       "nvhs_uphy_l0",
						       "nvhs_uphy_l1",
						       "nvhs_uphy_l2",
						       "nvhs_uphy_l3",
						       "nvhs_uphy_l4",
						       "nvhs_uphy_l5",
						       "nvhs_uphy_l6",
						       "nvhs_uphy_l7",
						       "nvhs_uphy_pm",
						       "dmic5",
						       "ape",
						       "pex_usb_uphy",
						       "pex_usb_uphy_l0",
						       "pex_usb_uphy_l1",
						       "pex_usb_uphy_l2",
						       "pex_usb_uphy_l3",
						       "pex_usb_uphy_l4",
						       "pex_usb_uphy_l5",
						       "pex_usb_uphy_l6",
						       "pex_usb_uphy_l7",
						       "pex_usb_uphy_pll0",
						       "pex_usb_uphy_pll1",
						       "pex_usb_uphy_pll2",
						       "pex_usb_uphy_pll3",
						       "gbe_uphy_l0",
						       "gbe_uphy_l1",
						       "gbe_uphy_l2",
						       "gbe_uphy_l3",
						       "gbe_uphy_l4",
						       "gbe_uphy_l5",
						       "gbe_uphy_l6",
						       "gbe_uphy_l7",
						       "gbe_uphy_pll0",
						       "gbe_uphy_pll1",
						       "gbe_uphy_pll2";
				};
			};

			mods_smmu: mods_smmu {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso0 TEGRA234_SID_SMMU_TEST>;
				non-coherent;
				dev-names = "mods_smmu";
				status = "disabled";
			};

			mods_pcie0: mods_pcie0 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso0 TEGRA234_SID_PCIE0>;
				dma-coherent;
				dev-names = "mods_pcie0";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x0>;
			};

			mods_pcie1: mods_pcie1 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_PCIE1>;
				dma-coherent;
				dev-names = "mods_pcie1";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x1>;
			};

			mods_pcie2: mods_pcie2 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_PCIE2>;
				dma-coherent;
				dev-names = "mods_pcie2";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x2>;
			};

			mods_pcie3: mods_pcie3 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_PCIE3>;
				dma-coherent;
				dev-names = "mods_pcie3";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x3>;
			};

			mods_pcie4: mods_pcie4 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso0 TEGRA234_SID_PCIE4>;
				dma-coherent;
				dev-names = "mods_pcie4";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x4>;
			};

			mods_pcie5: mods_pcie5 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso0 TEGRA234_SID_PCIE5>;
				dma-coherent;
				dev-names = "mods_pcie5";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x5>;
			};

			mods_pcie6: mods_pcie6 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso0 TEGRA234_SID_PCIE6>;
				dma-coherent;
				dev-names = "mods_pcie6";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x6>;
			};

			mods_pcie7: mods_pcie7 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_PCIE7>;
				dma-coherent;
				dev-names = "mods_pcie7";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x7>;
			};

			mods_pcie8: mods_pcie8 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_PCIE8>;
				dma-coherent;
				dev-names = "mods_pcie8";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x8>;
			};

			mods_pcie9: mods_pcie9 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso0 TEGRA234_SID_PCIE9>;
				dma-coherent;
				dev-names = "mods_pcie9";
				status = "disabled";
				nvidia,bpmp = <&bpmp 0x9>;
			};

			mods_pcie10: mods_pcie10 {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_PCIE10>;
				dma-coherent;
				dev-names = "mods_pcie10";
				status = "disabled";
				nvidia,bpmp = <&bpmp 10>;
			};

			mods_isp: mods_isp {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_ISP>;
				dma-coherent;
				dev-names = "mods_isp";
				status = "disabled";
			};

			mods_test: mods_test {
				compatible = "nvidia,mods_test";
				status = "disabled";
			};

			mods_dma: mods_dma {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso0 TEGRA234_SID_GPCDMA>;
				dma-coherent;
				dev-names = "mods_dma";
				status = "disabled";
			};

			mods_qspi0_dma: mods_qspi0_dma {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_QSPI0>;
				dma-coherent;
				dev-names = "mods_qspi0_dma";
				status = "disabled";
			};

			mods_qspi1_dma: mods_qspi1_dma {
				compatible = "nvidia,mods_smmu";
				iommus = <&smmu_niso1 TEGRA234_SID_QSPI1>;
				dma-coherent;
				dev-names = "mods_qspi1_dma";
				status = "disabled";
			};
		};
	};
};
