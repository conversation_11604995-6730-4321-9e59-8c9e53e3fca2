// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2023-2024, NVIDIA CORPORATION & AFFILIATES. All rights reserved.

/* 12 camera module with 4 hawks and 4 Owls
 * HAWK1 - 2 ar0234 cameras - max96712 Aggregator 1 - GMSL0
 * HAWK2 - 2 ar0234 cameras - max96712 Aggregator 1 - GMSL0
 * HAWK3 - 2 ar0234 cameras - max96712 Aggregator 1 - GMSL0
 * HAWK4 - 2 ar0234 cameras - max96712 Aggregator 1 - GMSL0
 * OWL1  - 1 ar0234 camera  - max96712 Aggregator 2 - GMSL1
 * OWL2  - 1 ar0234 camera  - max96712 Aggregator 2 - GMSL1
 * OWL3  - 1 ar0234 camera  - max96712 Aggregator 2 - GMSL1
 * OWL4  - 1 ar0234 camera  - max96712 Aggregator 2 - GMSL1
 */
/ {
	fragment-camera@0 {
		target-path = "/";
		__overlay__ {
			tegra-capture-vi {
				num-channels = <12>;
				ports {
					#address-cells = <1>;
					#size-cells = <0>;
					port@0 {
						reg = <0>;
						status = "okay";
						ar0234_vi_in0: endpoint {
							vc-id = <0>;
							port-index = <0>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out0>;
							status = "okay";
						};
					};
					port@1 {
						reg = <1>;
						status = "okay";
						ar0234_vi_in1: endpoint {
							vc-id = <1>;
							port-index = <0>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out1>;
							status = "okay";
						};
					};
					port@2 {
						reg = <2>;
						status = "okay";
						ar0234_vi_in2: endpoint {
							vc-id = <0>;
							port-index = <1>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out2>;
							status = "okay";
						};
					};
					port@3 {
						reg = <3>;
						status = "okay";
						ar0234_vi_in3: endpoint {
							vc-id = <1>;
							port-index = <1>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out3>;
							status = "okay";
						};
					};
					port@4 {
						reg = <4>;
						status = "okay";
						ar0234_vi_in4: endpoint {
							vc-id = <0>;
							port-index = <2>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out4>;
							status = "okay";
						};
					};
					port@5 {
						reg = <5>;
						status = "okay";
						ar0234_vi_in5: endpoint {
							vc-id = <1>;
							port-index = <2>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out5>;
							status = "okay";
						};
					};
					port@6 {
						reg = <6>;
						status = "okay";
						ar0234_vi_in6: endpoint {
							vc-id = <0>;
							port-index = <3>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out6>;
							status = "okay";
						};
					};
					port@7 {
						reg = <7>;
						status = "okay";
						ar0234_vi_in7: endpoint {
						vc-id = <1>;
							port-index = <3>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out7>;
							status = "okay";
						};
					};
					port@8 {
						reg = <8>;
						status = "okay";
						ar0234_vi_in8: endpoint {
							vc-id = <0>;
							port-index = <4>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out8>;
							status = "okay";
						};
					};
					port@9 {
						reg = <9>;
						status = "okay";
						ar0234_vi_in9: endpoint {
							vc-id = <1>;
							port-index = <4>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out9>;
							status = "okay";
						};
					};
					port@10 {
						reg = <10>;
						status = "okay";
						ar0234_vi_in10: endpoint {
							vc-id = <2>;
							port-index = <4>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out10>;
							status = "okay";
						};
					};
					port@11 {
						reg = <11>;
						status = "okay";
						ar0234_vi_in11: endpoint {
							vc-id = <3>;
							port-index = <4>;
							bus-width = <2>;
							remote-endpoint = <&ar0234_csi_out11>;
							status = "okay";
						};
					};
				};
			};
			bus@0 {
				host1x@13e00000 {
					nvcsi@15a00000 {
						num-channels = <12>;
						#address-cells = <1>;
						#size-cells = <0>;
						channel@0 {
							reg = <0>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in0: endpoint@0 {
										port-index = <0>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out0>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out0: endpoint@1 {
										remote-endpoint = <&ar0234_vi_in0>;
										status = "okay";
									};
								};
							};
						};
						channel@1 {
							reg = <1>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in1: endpoint@2 {
										port-index = <0>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out1>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out1: endpoint@3 {
										remote-endpoint = <&ar0234_vi_in1>;
										status = "okay";
									};
								};
							};
						};
						channel@2 {
							reg = <2>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in2: endpoint@4 {
										port-index = <1>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out2>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out2: endpoint@5 {
										remote-endpoint = <&ar0234_vi_in2>;
										status = "okay";
									};
								};
							};
						};
						channel@3 {
							reg = <3>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in3: endpoint@6 {
										port-index = <1>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out3>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out3: endpoint@7 {
										remote-endpoint = <&ar0234_vi_in3>;
										status = "okay";
									};
								};
							};
						};
						channel@4 {
							reg = <4>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in4: endpoint@8 {
										port-index = <2>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out4>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out4: endpoint@9 {
										remote-endpoint = <&ar0234_vi_in4>;
										status = "okay";
									};
								};
							};
						};
						channel@5 {
							reg = <5>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in5: endpoint@10 {
										port-index = <2>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out5>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out5: endpoint@11 {
										remote-endpoint = <&ar0234_vi_in5>;
										status = "okay";
									};
								};
							};
						};
						channel@6 {
							reg = <6>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in6: endpoint@12 {
										port-index = <3>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out6>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out6: endpoint@13 {
										remote-endpoint = <&ar0234_vi_in6>;
										status = "okay";
									};
								};
							};
						};
						channel@7 {
							reg = <7>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in7: endpoint@14 {
										port-index = <3>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out7>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out7: endpoint@15 {
										remote-endpoint = <&ar0234_vi_in7>;
										status = "okay";
									};
								};
							};
						};
						channel@8 {
							reg = <8>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in8: endpoint@16 {
										port-index = <4>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out8>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out8: endpoint@17 {
										remote-endpoint = <&ar0234_vi_in8>;
										status = "okay";
									};
								};
							};
						};
						channel@9 {
							reg = <9>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in9: endpoint@18 {
										port-index = <4>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out9>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out9: endpoint@19 {
										remote-endpoint = <&ar0234_vi_in9>;
										status = "okay";
									};
								};
							};
						};
						channel@10 {
							reg = <10>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in10: endpoint@20 {
										port-index = <4>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out10>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out10: endpoint@21 {
										remote-endpoint = <&ar0234_vi_in10>;
										status = "okay";
									};
								};
							};
						};
						channel@11 {
							reg = <11>;
							status = "okay";
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									status = "okay";
									ar0234_csi_in11: endpoint@22 {
										port-index = <4>;
										bus-width = <2>;
										remote-endpoint = <&ar0234_ar0234_out11>;
										status = "okay";
									};
								};
								port@1 {
									reg = <1>;
									status = "okay";
									ar0234_csi_out11: endpoint@23 {
										remote-endpoint = <&ar0234_vi_in11>;
										status = "okay";
									};
								};
							};
						};
					};
				};
				i2c@3180000 {
					ar0234_i@30 {
						compatible = "nvidia,ar0234_hawk_owl";
						reg = <0x30>;
						/* Physical dimensions of sensor */
						physical_w = "15.0";
						physical_h = "12.5";
						sensor_model ="ar0234";
						sync_sensor = "OWL1";
						sync_sensor_index = <1>;
						/* Defines number of frames to be dropped by driver internally after applying */
						/* sensor crop settings. Some sensors send corrupt frames after applying */
						/* crop co-ordinates */
						post_crop_frame_drop = "0";
						/* enable CID_SENSOR_MODE_ID for sensor modes selection */
						use_sensor_mode_id = "true";
						mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
							mclk_khz = "24000";
							num_lanes = "2";
							tegra_sinterface = "serial_e";
							vc_id = "0";
							discontinuous_clk = "no";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer";
							pixel_phase = "grbg";
							active_w = "1920";
							active_h = "1200";
							readout_orientation = "0";
							line_length = "2448";
							inherent_gain = "1";
							mclk_multiplier = "3.01";
							pix_clk_hz = "134400000";
							serdes_pix_clk_hz = "299000000";
							gain_factor = "100";
							min_gain_val = "100"; /* dB */
							max_gain_val = "1600"; /* dB */
							step_gain_val = "1"; /* 0.1 */
							default_gain = "100";
							min_hdr_ratio = "1";
							max_hdr_ratio = "1";
							framerate_factor = "1000000";
							min_framerate = "30000000";
							max_framerate = "30000000";
							step_framerate = "30000000";
							default_framerate = "30000000";
							exposure_factor = "1000000";
							min_exp_time = "28"; /*us, 2 lines*/
							max_exp_time = "22000";
							step_exp_time = "1";
							default_exp_time = "22000";/* us */
							embedded_metadata_height = "0";
						};
						ports {
							#address-cells = <1>;
							#size-cells = <0>;
							port@0 {
								reg = <0>;
								ar0234_ar0234_out8: endpoint {
									vc-id = <0>;
									port-index = <4>;
									bus-width = <2>;
									remote-endpoint = <&ar0234_csi_in8>;
								};
							};
						};
					};
					ar0234_j@32 {
						compatible = "nvidia,ar0234_hawk_owl";
						reg = <0x32>;
						/* Physical dimensions of sensor */
						physical_w = "15.0";
						physical_h = "12.5";
						sensor_model ="ar0234";
						sync_sensor = "OWL2";
						sync_sensor_index = <1>;
						/* Defines number of frames to be dropped by driver internally after applying */
						/* sensor crop settings. Some sensors send corrupt frames after applying */
						/* crop co-ordinates */
						post_crop_frame_drop = "0";
						/* enable CID_SENSOR_MODE_ID for sensor modes selection */
						use_sensor_mode_id = "true";
						mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
							mclk_khz = "24000";
							num_lanes = "2";
							tegra_sinterface = "serial_e";
							vc_id = "1";
							discontinuous_clk = "no";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer";
							pixel_phase = "grbg";
							active_w = "1920";
							active_h = "1200";
							readout_orientation = "0";
							line_length = "2448";
							inherent_gain = "1";
							mclk_multiplier = "3.01";
							pix_clk_hz = "134400000";
							serdes_pix_clk_hz = "299000000";
							gain_factor = "100";
							min_gain_val = "100"; /* dB */
							max_gain_val = "1600"; /* dB */
							step_gain_val = "1"; /* 0.1 */
							default_gain = "100";
							min_hdr_ratio = "1";
							max_hdr_ratio = "1";
							framerate_factor = "1000000";
							min_framerate = "30000000";
							max_framerate = "30000000";
							step_framerate = "30000000";
							default_framerate = "30000000";
							exposure_factor = "1000000";
							min_exp_time = "28"; /*us, 2 lines*/
							max_exp_time = "22000";
							step_exp_time = "1";
							default_exp_time = "22000";/* us */
							embedded_metadata_height = "0";
						};
						ports {
							#address-cells = <1>;
							#size-cells = <0>;
							port@0 {
								reg = <0>;
								ar0234_ar0234_out9: endpoint {
									vc-id = <1>;
									port-index = <4>;
									bus-width = <2>;
									remote-endpoint = <&ar0234_csi_in9>;
								};
							};
						};
					};
					ar0234_k@34 {
						compatible = "nvidia,ar0234_hawk_owl";
						reg = <0x34>;
						/* Physical dimensions of sensor */
						physical_w = "15.0";
						physical_h = "12.5";
						sensor_model ="ar0234";
						sync_sensor = "OWL3";
						sync_sensor_index = <1>;
						/* Defines number of frames to be dropped by driver internally after applying */
						/* sensor crop settings. Some sensors send corrupt frames after applying */
						/* crop co-ordinates */
						post_crop_frame_drop = "0";
						/* enable CID_SENSOR_MODE_ID for sensor modes selection */
						use_sensor_mode_id = "true";
						mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
							mclk_khz = "24000";
							num_lanes = "2";
							tegra_sinterface = "serial_e";
							vc_id = "2";
							discontinuous_clk = "no";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer";
							pixel_phase = "grbg";
							active_w = "1920";
							active_h = "1200";
							readout_orientation = "0";
							line_length = "2448";
							inherent_gain = "1";
							mclk_multiplier = "3.01";
							pix_clk_hz = "134400000";
							serdes_pix_clk_hz = "299000000";
							gain_factor = "100";
							min_gain_val = "100"; /* dB */
							max_gain_val = "1600"; /* dB */
							step_gain_val = "1"; /* 0.1 */
							default_gain = "100";
							min_hdr_ratio = "1";
							max_hdr_ratio = "1";
							framerate_factor = "1000000";
							min_framerate = "30000000";
							max_framerate = "30000000";
							step_framerate = "30000000";
							default_framerate = "30000000";
							exposure_factor = "1000000";
							min_exp_time = "28"; /*us, 2 lines*/
							max_exp_time = "22000";
							step_exp_time = "1";
							default_exp_time = "22000";/* us */
							embedded_metadata_height = "0";
						};
						ports {
							#address-cells = <1>;
							#size-cells = <0>;
							port@0 {
								reg = <0>;
								ar0234_ar0234_out10: endpoint {
									vc-id = <2>;
									port-index = <4>;
									bus-width = <2>;
									remote-endpoint = <&ar0234_csi_in10>;
								};
							};
						};
					};
					ar0234_l@36 {
						compatible = "nvidia,ar0234_hawk_owl";
						reg = <0x36>;
						/* Physical dimensions of sensor */
						physical_w = "15.0";
						physical_h = "12.5";
						sensor_model ="ar0234";
						sync_sensor = "OWL4";
						sync_sensor_index = <1>;
						/* Defines number of frames to be dropped by driver internally after applying */
						/* sensor crop settings. Some sensors send corrupt frames after applying */
						/* crop co-ordinates */
						post_crop_frame_drop = "0";
						/* enable CID_SENSOR_MODE_ID for sensor modes selection */
						use_sensor_mode_id = "true";
						mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
							mclk_khz = "24000";
							num_lanes = "2";
							tegra_sinterface = "serial_e";
							vc_id = "3";
							discontinuous_clk = "no";
							dpcm_enable = "false";
							cil_settletime = "0";
							dynamic_pixel_bit_depth = "10";
							csi_pixel_bit_depth = "10";
							mode_type = "bayer";
							pixel_phase = "grbg";
							active_w = "1920";
							active_h = "1200";
							readout_orientation = "0";
							line_length = "2448";
							inherent_gain = "1";
							mclk_multiplier = "3.01";
							pix_clk_hz = "134400000";
							serdes_pix_clk_hz = "299000000";
							gain_factor = "100";
							min_gain_val = "100"; /* dB */
							max_gain_val = "1600"; /* dB */
							step_gain_val = "1"; /* 0.1 */
							default_gain = "100";
							min_hdr_ratio = "1";
							max_hdr_ratio = "1";
							framerate_factor = "1000000";
							min_framerate = "30000000";
							max_framerate = "30000000";
							step_framerate = "30000000";
							default_framerate = "30000000";
							exposure_factor = "1000000";
							min_exp_time = "28"; /*us, 2 lines*/
							max_exp_time = "22000";
							step_exp_time = "1";
							default_exp_time = "22000";/* us */
							embedded_metadata_height = "0";
						};
						ports {
							#address-cells = <1>;
							#size-cells = <0>;
							port@0 {
								reg = <0>;
								ar0234_ar0234_out11: endpoint {
									vc-id = <3>;
									port-index = <4>;
									bus-width = <2>;
									remote-endpoint = <&ar0234_csi_in11>;
								};
							};
						};
					};
				};
				/*i2c8*/
				i2c@31e0000 {
					/* Set i2c freq to 400khz */
					clock-frequency = <400000>;
					virtual_i2c_mux@50 {
						i2c@0 {
							ar0234_a@30 {
								compatible = "nvidia,ar0234_hawk_owl";
								reg = <0x30>;
								/* Physical dimensions of sensor */
								physical_w = "15.0";
								physical_h = "12.5";
								sensor_model ="ar0234";
								sync_sensor = "HAWK1";
								sync_sensor_index = <1>;
								/* Defines number of frames to be dropped by driver internally after applying */
								/* sensor crop settings. Some sensors send corrupt frames after applying */
								/* crop co-ordinates */
								post_crop_frame_drop = "0";
								/* enable CID_SENSOR_MODE_ID for sensor modes selection */
								use_sensor_mode_id = "true";
								/**
								 * A modeX node is required to support v4l2 driver
								 * implementation with NVIDIA camera software stack
								 *
								 * mclk_khz = "";
								 * Standard MIPI driving clock, typically 24MHz
								 *
								 * num_lanes = "";
								 * Number of lane channels sensor is programmed to output
								 *
								 * tegra_sinterface = "";
								 * The base tegra serial interface lanes are connected to
								 *
								 * vc_id = "";
								 * The virtual channel id of the sensor.
								 *
								 * discontinuous_clk = "";
								 * The sensor is programmed to use a discontinuous clock on MIPI lanes
								 *
								 * dpcm_enable = "true";
								 * The sensor is programmed to use a DPCM modes
								 *
								 * cil_settletime = "";
								 * MIPI lane settle time value.
								 * A "0" value attempts to autocalibrate based on mclk_khz and pix_clk_hz
								 *
								 * active_w = "";
								 * Pixel active region width
								 *
								 * active_h = "";
								 * Pixel active region height
								 *
								 * dynamic_pixel_bit_depth = "";
								 * sensor dynamic bit depth for sensor mode
								 *
								 * csi_pixel_bit_depth = "";
								 * sensor output bit depth for sensor mode
								 *
								 * mode_type="";
								 * Sensor mode type, For eg: yuv, Rgb, bayer, bayer_wdr_pwl
								 *
								 * pixel_phase="";
								 * Pixel phase for sensor mode, For eg: rggb, vyuy, rgb888
								 *
								 * readout_orientation = "0";
								 * Based on camera module orientation.
								 * Only change readout_orientation if you specifically
								 * Program a different readout order for this mode
								 *
								 * line_length = "";
								 * Pixel line length (width) for sensor mode.
								 * This is used to calibrate features in our camera stack.
								 *
								 * pix_clk_hz = "";
								 * Sensor pixel clock used for calculations like exposure and framerate
								 *
								 *
								 *
								 *
								 * inherent_gain = "";
								 * Gain obtained inherently from mode (ie. pixel binning)
								 *
								 * min_gain_val = ""; (floor to 6 decimal places)
								 * max_gain_val = ""; (floor to 6 decimal places)
								 * Gain limits for mode
								 * if use_decibel_gain = "true", please set the gain as decibel
								 *
								 * min_exp_time = ""; (ceil to integer)
								 * max_exp_time = ""; (ceil to integer)
								 * Exposure Time limits for mode (us)
								 *
								 *
								 * min_hdr_ratio = "";
								 * max_hdr_ratio = "";
								 * HDR Ratio limits for mode
								 *
								 * min_framerate = "";
								 * max_framerate = "";
								 * Framerate limits for mode (fps)
								 *
								 * embedded_metadata_height = "";
								 * Sensor embedded metadata height in units of rows.
								 * If sensor does not support embedded metadata value should be 0.
								*/
								mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
									mclk_khz = "24000";
									num_lanes = "2";
									tegra_sinterface = "serial_a";
									phy_mode = "DPHY";
									vc_id = "0";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "10";
									csi_pixel_bit_depth = "10";
									mode_type = "bayer";
									pixel_phase = "grbg";
									active_w = "1920";
									active_h = "1200";
									readout_orientation = "0";
									line_length = "2448";
									inherent_gain = "1";
									mclk_multiplier = "3.01";
									pix_clk_hz = "134400000";
									serdes_pix_clk_hz = "299000000";
									gain_factor = "100";
									min_gain_val = "100"; /* dB */
									max_gain_val = "1600"; /* dB */
									step_gain_val = "1"; /* 0.1 */
									default_gain = "100";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									framerate_factor = "1000000";
									min_framerate = "30000000";
									max_framerate = "30000000";
									step_framerate = "30000000";
									default_framerate = "30000000";
									exposure_factor = "1000000";
									min_exp_time = "28"; /*us, 2 lines*/
									max_exp_time = "22000";
									step_exp_time = "1";
									default_exp_time = "22000";/* us */
									embedded_metadata_height = "0";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ar0234_ar0234_out0: endpoint {
											vc-id = <0>;
											port-index = <0>;
											bus-width = <2>;
											remote-endpoint = <&ar0234_csi_in0>;
										};
									};
								};
							};
							ar0234_b@31 {
								compatible = "nvidia,ar0234_hawk_owl";
								reg = <0x31>;
								/* Physical dimensions of sensor */
								physical_w = "15.0";
								physical_h = "12.5";
								sensor_model ="ar0234";
								sync_sensor = "HAWK1";
								sync_sensor_index = <2>;
								/* Defines number of frames to be dropped by driver internally after applying */
								/* sensor crop settings. Some sensors send corrupt frames after applying */
								/* crop co-ordinates */
								post_crop_frame_drop = "0";
								/* enable CID_SENSOR_MODE_ID for sensor modes selection */
								use_sensor_mode_id = "true";
								mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
									mclk_khz = "24000";
									num_lanes = "2";
									tegra_sinterface = "serial_a";
									vc_id = "1";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "10";
									csi_pixel_bit_depth = "10";
									mode_type = "bayer";
									pixel_phase = "grbg";
									active_w = "1920";
									active_h = "1200";
									readout_orientation = "0";
									line_length = "2448";
									inherent_gain = "1";
									mclk_multiplier = "3.01";
									pix_clk_hz = "134400000";
									serdes_pix_clk_hz = "299000000";
									gain_factor = "100";
									min_gain_val = "100"; /* dB */
									max_gain_val = "1600"; /* dB */
									step_gain_val = "1"; /* 0.1 */
									default_gain = "100";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									framerate_factor = "1000000";
									min_framerate = "30000000";
									max_framerate = "30000000";
									step_framerate = "30000000";
									default_framerate = "30000000";
									exposure_factor = "1000000";
									min_exp_time = "28"; /*us, 2 lines*/
									max_exp_time = "22000";
									step_exp_time = "1";
									default_exp_time = "22000";/* us */
									embedded_metadata_height = "0";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ar0234_ar0234_out1: endpoint {
											vc-id = <1>;
											port-index = <0>;
											bus-width = <2>;
											remote-endpoint = <&ar0234_csi_in1>;
										};
									};
								};
							};
						};
						i2c@1 {
							ar0234_c@32 {
								compatible = "nvidia,ar0234_hawk_owl";
								reg = <0x32>;
								/* Physical dimensions of sensor */
								physical_w = "15.0";
								physical_h = "12.5";
								sensor_model ="ar0234";
								sync_sensor = "HAWK2";
								sync_sensor_index = <1>;
								/* Defines number of frames to be dropped by driver internally after applying */
								/* sensor crop settings. Some sensors send corrupt frames after applying */
								/* crop co-ordinates */
								post_crop_frame_drop = "0";
								/* enable CID_SENSOR_MODE_ID for sensor modes selection */
								use_sensor_mode_id = "true";
								mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
									mclk_khz = "24000";
									num_lanes = "2";
									tegra_sinterface = "serial_b";
									vc_id = "0";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "10";
									csi_pixel_bit_depth = "10";
									mode_type = "bayer";
									pixel_phase = "grbg";
									active_w = "1920";
									active_h = "1200";
									readout_orientation = "0";
									line_length = "2448";
									inherent_gain = "1";
									mclk_multiplier = "3.01";
									pix_clk_hz = "134400000";
									serdes_pix_clk_hz = "299000000";
									gain_factor = "100";
									min_gain_val = "100"; /* dB */
									max_gain_val = "1600"; /* dB */
									step_gain_val = "1"; /* 0.1 */
									default_gain = "100";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									framerate_factor = "1000000";
									min_framerate = "30000000";
									max_framerate = "30000000";
									step_framerate = "30000000";
									default_framerate = "30000000";
									exposure_factor = "1000000";
									min_exp_time = "28"; /*us, 2 lines*/
									max_exp_time = "22000";
									step_exp_time = "1";
									default_exp_time = "22000";/* us */
									embedded_metadata_height = "0";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ar0234_ar0234_out2: endpoint {
											vc-id = <0>;
											port-index = <1>;
											bus-width = <2>;
											remote-endpoint = <&ar0234_csi_in2>;
										};
									};
								};
							};
							ar0234_d@33 {
								compatible = "nvidia,ar0234_hawk_owl";
								reg = <0x33>;
								/* Physical dimensions of sensor */
								physical_w = "15.0";
								physical_h = "12.5";
								sensor_model ="ar0234";
								sync_sensor = "HAWK2";
								sync_sensor_index = <2>;
								/* Defines number of frames to be dropped by driver internally after applying */
								/* sensor crop settings. Some sensors send corrupt frames after applying */
								/* crop co-ordinates */
								post_crop_frame_drop = "0";
								/* enable CID_SENSOR_MODE_ID for sensor modes selection */
								use_sensor_mode_id = "true";
								mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
									mclk_khz = "24000";
									num_lanes = "2";
									tegra_sinterface = "serial_b";
									vc_id = "1";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "10";
									csi_pixel_bit_depth = "10";
									mode_type = "bayer";
									pixel_phase = "grbg";
									active_w = "1920";
									active_h = "1200";
									readout_orientation = "0";
									line_length = "2448";
									inherent_gain = "1";
									mclk_multiplier = "3.01";
									pix_clk_hz = "134400000";
									serdes_pix_clk_hz = "299000000";
									gain_factor = "100";
									min_gain_val = "100"; /* dB */
									max_gain_val = "1600"; /* dB */
									step_gain_val = "1"; /* 0.1 */
									default_gain = "100";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									framerate_factor = "1000000";
									min_framerate = "30000000";
									max_framerate = "30000000";
									step_framerate = "30000000";
									default_framerate = "30000000";
									exposure_factor = "1000000";
									min_exp_time = "28"; /*us, 2 lines*/
									max_exp_time = "22000";
									step_exp_time = "1";
									default_exp_time = "22000";/* us */
									embedded_metadata_height = "0";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ar0234_ar0234_out3: endpoint {
											vc-id = <1>;
											port-index = <1>;
											bus-width = <2>;
											remote-endpoint = <&ar0234_csi_in3>;
										};
									};
								};
							};
							ar0234_e@34 {
								compatible = "nvidia,ar0234_hawk_owl";
								reg = <0x34>;
								/* Physical dimensions of sensor */
								physical_w = "15.0";
								physical_h = "12.5";
								sensor_model ="ar0234";
								sync_sensor = "HAWK3";
								sync_sensor_index = <1>;
								/* Defines number of frames to be dropped by driver internally after applying */
								/* sensor crop settings. Some sensors send corrupt frames after applying */
								/* crop co-ordinates */
								post_crop_frame_drop = "0";
								/* enable CID_SENSOR_MODE_ID for sensor modes selection */
								use_sensor_mode_id = "true";
								mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
									mclk_khz = "24000";
									num_lanes = "2";
									tegra_sinterface = "serial_c";
									vc_id = "0";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "10";
									csi_pixel_bit_depth = "10";
									mode_type = "bayer";
									pixel_phase = "grbg";
									active_w = "1920";
									active_h = "1200";
									readout_orientation = "0";
									line_length = "2448";
									inherent_gain = "1";
									mclk_multiplier = "3.01";
									pix_clk_hz = "134400000";
									serdes_pix_clk_hz = "299000000";
									gain_factor = "100";
									min_gain_val = "100"; /* dB */
									max_gain_val = "1600"; /* dB */
									step_gain_val = "1"; /* 0.1 */
									default_gain = "100";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									framerate_factor = "1000000";
									min_framerate = "30000000";
									max_framerate = "30000000";
									step_framerate = "30000000";
									default_framerate = "30000000";
									exposure_factor = "1000000";
									min_exp_time = "28"; /*us, 2 lines*/
									max_exp_time = "22000";
									step_exp_time = "1";
									default_exp_time = "22000";/* us */
									embedded_metadata_height = "0";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ar0234_ar0234_out4: endpoint {
											vc-id = <0>;
											port-index = <2>;
											bus-width = <2>;
											remote-endpoint = <&ar0234_csi_in4>;
										};
									};
								};
							};
							ar0234_f@35 {
								compatible = "nvidia,ar0234_hawk_owl";
								reg = <0x35>;
								/* Physical dimensions of sensor */
								physical_w = "15.0";
								physical_h = "12.5";
								sensor_model ="ar0234";
								sync_sensor = "HAWK3";
								sync_sensor_index = <2>;
								/* Defines number of frames to be dropped by driver internally after applying */
								/* sensor crop settings. Some sensors send corrupt frames after applying */
								/* crop co-ordinates */
								post_crop_frame_drop = "0";
								/* enable CID_SENSOR_MODE_ID for sensor modes selection */
								use_sensor_mode_id = "true";
								mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
									mclk_khz = "24000";
									num_lanes = "2";
									tegra_sinterface = "serial_c";
									vc_id = "1";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "10";
									csi_pixel_bit_depth = "10";
									mode_type = "bayer";
									pixel_phase = "grbg";
									active_w = "1920";
									active_h = "1200";
									readout_orientation = "0";
									line_length = "2448";
									inherent_gain = "1";
									mclk_multiplier = "3.01";
									pix_clk_hz = "134400000";
									serdes_pix_clk_hz = "299000000";
									gain_factor = "100";
									min_gain_val = "100"; /* dB */
									max_gain_val = "1600"; /* dB */
									step_gain_val = "1"; /* 0.1 */
									default_gain = "100";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									framerate_factor = "1000000";
									min_framerate = "30000000";
									max_framerate = "30000000";
									step_framerate = "30000000";
									default_framerate = "30000000";
									exposure_factor = "1000000";
									min_exp_time = "28"; /*us, 2 lines*/
									max_exp_time = "22000";
									step_exp_time = "1";
									default_exp_time = "22000";/* us */
									embedded_metadata_height = "0";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ar0234_ar0234_out5: endpoint {
											vc-id = <1>;
											port-index = <2>;
											bus-width = <2>;
											remote-endpoint = <&ar0234_csi_in5>;
										};
									};
								};
							};
							ar0234_g@36 {
								compatible = "nvidia,ar0234_hawk_owl";
								reg = <0x36>;
								/* Physical dimensions of sensor */
								physical_w = "15.0";
								physical_h = "12.5";
								sensor_model ="ar0234";
								sync_sensor = "HAWK4";
								sync_sensor_index = <1>;
								/* Defines number of frames to be dropped by driver internally after applying */
								/* sensor crop settings. Some sensors send corrupt frames after applying */
								/* crop co-ordinates */
								post_crop_frame_drop = "0";
								/* enable CID_SENSOR_MODE_ID for sensor modes selection */
								use_sensor_mode_id = "true";
								mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
									mclk_khz = "24000";
									num_lanes = "2";
									tegra_sinterface = "serial_d";
									vc_id = "0";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "10";
									csi_pixel_bit_depth = "10";
									mode_type = "bayer";
									pixel_phase = "grbg";
									active_w = "1920";
									active_h = "1200";
									readout_orientation = "0";
									line_length = "2448";
									inherent_gain = "1";
									mclk_multiplier = "3.01";
									pix_clk_hz = "134400000";
									serdes_pix_clk_hz = "299000000";
									gain_factor = "100";
									min_gain_val = "100"; /* dB */
									max_gain_val = "1600"; /* dB */
									step_gain_val = "1"; /* 0.1 */
									default_gain = "100";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									framerate_factor = "1000000";
									min_framerate = "30000000";
									max_framerate = "30000000";
									step_framerate = "30000000";
									default_framerate = "30000000";
									exposure_factor = "1000000";
									min_exp_time = "28"; /*us, 2 lines*/
									max_exp_time = "22000";
									step_exp_time = "1";
									default_exp_time = "22000";/* us */
									embedded_metadata_height = "0";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ar0234_ar0234_out6: endpoint {
											vc-id = <0>;
											port-index = <3>;
											bus-width = <2>;
											remote-endpoint = <&ar0234_csi_in6>;
										};
									};
								};
							};
							ar0234_h@37 {
								compatible = "nvidia,ar0234_hawk_owl";
								reg = <0x37>;
								/* Physical dimensions of sensor */
								physical_w = "15.0";
								physical_h = "12.5";
								sensor_model ="ar0234";
								sync_sensor = "HAWK4";
								sync_sensor_index = <2>;
								/* Defines number of frames to be dropped by driver internally after applying */
								/* sensor crop settings. Some sensors send corrupt frames after applying */
								/* crop co-ordinates */
								post_crop_frame_drop = "0";
								/* enable CID_SENSOR_MODE_ID for sensor modes selection */
								use_sensor_mode_id = "true";
								mode0 {/*mode IMX424_MODE_3840X1080_CROP_30FPS*/
									mclk_khz = "24000";
									num_lanes = "2";
									tegra_sinterface = "serial_d";
									vc_id = "1";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "10";
									csi_pixel_bit_depth = "10";
									mode_type = "bayer";
									pixel_phase = "grbg";
									active_w = "1920";
									active_h = "1200";
									readout_orientation = "0";
									line_length = "2448";
									inherent_gain = "1";
									mclk_multiplier = "3.01";
									pix_clk_hz = "134400000";
									serdes_pix_clk_hz = "299000000";
									gain_factor = "100";
									min_gain_val = "100"; /* dB */
									max_gain_val = "1600"; /* dB */
									step_gain_val = "1"; /* 0.1 */
									default_gain = "100";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									framerate_factor = "1000000";
									min_framerate = "30000000";
									max_framerate = "30000000";
									step_framerate = "30000000";
									default_framerate = "30000000";
									exposure_factor = "1000000";
									min_exp_time = "28"; /*us, 2 lines*/
									max_exp_time = "22000";
									step_exp_time = "1";
									default_exp_time = "22000";/* us */
									embedded_metadata_height = "0";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ar0234_ar0234_out7: endpoint {
											vc-id = <1>;
											port-index = <3>;
											bus-width = <2>;
											remote-endpoint = <&ar0234_csi_in7>;
										};
									};
								};
							};
						};
					};
				};
			};
			tegra-camera-platform {
				compatible = "nvidia, tegra-camera-platform";
				/**
				 * Physical settings to calculate max ISO BW
				 *
				 * num_csi_lanes = <>;
				 * Total number of CSI lanes when all cameras are active
				 *
				 * max_lane_speed = <>;
				 * Max lane speed in Kbit/s
				 *
				 * min_bits_per_pixel = <>;
				 * Min bits per pixel
				 *
				 * vi_peak_byte_per_pixel = <>;
				 * Max byte per pixel for the VI ISO case
				 *
				 * vi_bw_margin_pct = <>;
				 * Vi bandwidth margin in percentage
				 *
				 * max_pixel_rate = <>;
				 * Max pixel rate in Kpixel/s for the ISP ISO case
				 *
				 * isp_peak_byte_per_pixel = <>;
				 * Max byte per pixel for the ISP ISO case
				 *
				 * isp_bw_margin_pct = <>;
				 * Isp bandwidth margin in percentage
				 */
				num_csi_lanes = <12>;
				max_lane_speed = <15000000>;
				min_bits_per_pixel = <10>;
				vi_peak_byte_per_pixel = <2>;
				vi_bw_margin_pct = <25>;
				isp_peak_byte_per_pixel = <5>;
				isp_bw_margin_pct = <25>;
				/**
				 * The general guideline for naming badge_info contains 3 parts, and is as follows,
				 * The first part is the camera_board_id for the module; if the module is in a FFD
				 * platform, then use the platform name for this part.
				 * The second part contains the position of the module, ex. "rear" or "front".
				 * The third part contains the last 6 characters of a part number which is found
				 * in the module's specsheet from the vender.
				 */
				modules {
					module0 {
						badge = "ar0234_bottomleft_NOVA0";
						position = "bottomleft";
						orientation = "1";
						status = "okay";
						drivernode0 {
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_sensor";
							/* Declare the device-tree hierarchy to driver instance */
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/ar0234_i@30";
							status = "okay";
						};
					};
					module1 {
						badge = "ar0234_centerleft_NOVA0";
						position = "centerleft";
						orientation = "1";
						status = "okay";
						drivernode0 {
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_sensor";
							/* Declare the device-tree hierarchy to driver instance */
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/ar0234_j@32";
							status = "okay";
						};
					};
					module2 {
						badge = "ar0234_centerright_NOVA0";
						position = "centerright";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/ar0234_k@34";
							status = "okay";
						};
					};
					module3 {
						badge = "ar0234_topleft_NOVA0";
						position = "topleft";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/ar0234_l@36";
							status = "okay";
						};
					};
					module4 {
						badge = "ar0234_bottomright_NOVA0";
						position = "bottomright";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@31e0000/virtual_i2c_mux@50/i2c@0/ar0234_a@30";
							status = "okay";
						};
					};
					module5 {
						badge = "ar0234_topright_NOVA0";
						position = "topright";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@31e0000/virtual_i2c_mux@50/i2c@0/ar0234_b@31";
							status = "okay";
						};
					};
					module6 {
						badge = "ar0234_bottomcenter_NOVA0";
						position = "bottomcenter";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@31e0000/virtual_i2c_mux@50/i2c@1/ar0234_c@32";
							status = "okay";
						};
					};
					module7 {
						badge = "ar0234_topcenter_NOVA0";
						position = "topcenter";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@31e0000/virtual_i2c_mux@50/i2c@1/ar0234_d@33";
							status = "okay";
						};
					};
					module8 {
						badge = "ar0234_frontcenter_NOVA0";
						position = "frontcenter";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@31e0000/virtual_i2c_mux@50/i2c@1/ar0234_e@34";
							status = "okay";
						};
					};
					module9 {
						badge = "ar0234_rearcenter_NOVA0";
						position = "rearcenter";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@31e0000/virtual_i2c_mux@50/i2c@1/ar0234_f@35";
							status = "okay";
						};
					};
					module10 {
						badge = "ar0234_leftcenter_NOVA0";
						position = "leftcenter";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@31e0000/virtual_i2c_mux@50/i2c@1/ar0234_g@36";
							status = "okay";
						};
					};
					module11 {
						badge = "ar0234_rightcenter_NOVA0";
						position = "rightcenter";
						orientation = "1";
						status = "okay";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@31e0000/virtual_i2c_mux@50/i2c@1/ar0234_h@37";
							status = "okay";
						};
					};
				};
			};
		};
	};
};
