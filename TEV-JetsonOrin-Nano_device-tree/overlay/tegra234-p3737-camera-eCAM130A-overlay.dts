/* SPDX-License-Identifier: GPL-2.0 */
/* SPDX-FileCopyrightText: Copyright (c) 2017-2023, NVIDIA CORPORATION & AFFILIATES. All rights reserved. */

/dts-v1/;
/plugin/;

#include <dt-bindings/clock/tegra234-clock.h>
#include <dt-bindings/gpio/tegra234-gpio.h>
#include <dt-bindings/tegra234-p3737-0000+p3701-0000.h>

#define IO_EXP_RST	TEGRA234_MAIN_GPIO(AC, 1)
#define MIPICFG0	TEGRA234_MAIN_GPIO(H, 3)
#define MIPICFG1	TEGRA234_MAIN_GPIO(H, 6)
#define MIPICFG2       TEGRA234_MAIN_GPIO(AC, 0)

#define PWM_CALIB_GPIO TEGRA234_MAIN_GPIO(AC, 7)

#define CAM_LANES 4
#define CAM_LANES_STRING "4"

/ {
	overlay-name = "Jetson Camera AR1335";
	jetson-header-name = "Jetson AGX CSI Connector";
	compatible = JETSON_COMPATIBLE;

	fragment@0 {
		target-path = "/";
		__overlay__ {
			bus@0 {
				gpio@2200000 {
					camera-control-output-low {
						gpio-hog;
						output-low;
						gpios =  <MIPICFG0 1 MIPICFG1 1 MIPICFG2 0>;
						label = "mipi_sel1", "mipi_sel2", "mipi_sel_3";
						status = "okay";
					};
				};
				i2c@c240000 {
					#address-cells = <1>;
					#size-cells = <0>;
					tca6424_22: tca6424@22 {
						compatible = "ti,tca6424";
						gpio-controller;
						#gpio-cells = <2>;
						reg = <0x22>;
						reset-gpios = <&gpio IO_EXP_RST GPIO_ACTIVE_LOW>;
						status = "okay";
					};
				};
				i2c@3180000 {
					#address-cells = <1>;
					#size-cells = <0>;
					tca9548@70 {
						status = "okay";
						compatible = "nxp,pca9548";
						reg = <0x70>;
						#address-cells = <1>;
						#size-cells = <0>;
						skip_mux_detect;
						i2c@0 {
							reg = <0>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ar1335_a@42 {
								status = "okay";
								compatible = "nvidia,ar1335";
								reg = <0x42>;
								sensor_model ="ar1335";
								avdd-reg = "vana";
								iovdd-reg = "vif";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
									 <&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								clock-frequency = <24000000>;
								reset-gpios = <&tca6424_22 0 GPIO_ACTIVE_HIGH>;
								pwdn-gpios = <&tca6424_22 6 GPIO_ACTIVE_HIGH>;
								use_sensor_mode_id = "false";
								camera_mipi_lanes = <CAM_LANES>;
								pwm-upper_limit = <342>;
								pwm-lower_limit = <340>;
								pwm-invert= <1>;
								pwm-duty_ns = <0x1CBC720>;
								pwm-period_ns = <0x2045B80>;
								pwm-calib-gpio = <&gpio PWM_CALIB_GPIO GPIO_ACTIVE_HIGH>;
								pwm-name = "ecam-trigger";
								mode0 { // MODE_640X480
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_a";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";

									active_w = "640";
									active_h = "480";
									pixel_phase = "uyvy";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "300000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									step_gain_val = "1";
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "100";
									step_framerate = "1";
									default_framerate = "30000000";
									min_exp_time = "1";
									max_exp_time = "43000000";
								};
								mode1 { // MODE_1280X720
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_a";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";
									active_w = "1280";
									active_h = "720";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "72";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode2 { // MODE_1920X1080
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_a";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "1920";
									active_h = "1080";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1.5";
									max_framerate = "72";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode3 { // MODE_3840X2160
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_a";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "3840";
									active_h = "2160";
									readout_orientation = "0";
									dskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "30";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode4 { // MODE_4096X2160
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_a";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "4096";
									active_h = "2160";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "19";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode5 { // MODE_4192X3120
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_a";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "4192";
									active_h = "3120";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "1152000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "19";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								ports {
								#address-cells = <1>;
								#size-cells = <0>;
									port@0 {
										reg = <0>;
										ecam_ar1335_out0: endpoint {
											port-index = <0>;
											bus-width = <CAM_LANES>;
											remote-endpoint = <&ecam_csi_in0>;
										};
									};
								};
							};
						};
						i2c@2{
							reg = <2>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ar1335_c@42 {
								status = "okay";
								compatible = "nvidia,ar1335";
								reg = <0x42>;
								sensor_model ="ar1335";
								avdd-reg = "vana";
								iovdd-reg = "vif";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
									 <&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								clock-frequency = <24000000>;
								reset-gpios = <&tca6424_22 2 GPIO_ACTIVE_HIGH>;
								pwdn-gpios = <&tca6424_22 8 GPIO_ACTIVE_HIGH>;
								use_sensor_mode_id = "false";
								camera_mipi_lanes = <CAM_LANES>;
								pwm-upper_limit = <342>;
								pwm-lower_limit = <340>;
								pwm-invert= <1>;
								pwm-duty_ns = <0x1CBC720>;
								pwm-period_ns = <0x2045B80>;
								pwm-calib-gpio = <&gpio PWM_CALIB_GPIO GPIO_ACTIVE_HIGH>;
								pwm-name = "ecam-trigger";
								mode0 { // MODE_640X480
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_c";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";

									active_w = "640";
									active_h = "480";
									pixel_phase = "uyvy";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "300000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									step_gain_val = "1";
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "100";
									step_framerate = "1";
									default_framerate = "30000000";
									min_exp_time = "1";
									max_exp_time = "43000000";
								};
								mode1 { // MODE_1280X720
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_c";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";
									active_w = "1280";
									active_h = "720";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "72";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode2 { // MODE_1920X1080
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_c";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "1920";
									active_h = "1080";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1.5";
									max_framerate = "72";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode3 { // MODE_3840X2160
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_c";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "3840";
									active_h = "2160";
									readout_orientation = "0";
									dskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "30";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode4 { // MODE_4096X2160
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_c";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "4096";
									active_h = "2160";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "19";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode5 { // MODE_4192X3120
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_c";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "4192";
									active_h = "3120";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "1152000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "19";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								ports {
								#address-cells = <1>;
								#size-cells = <0>;
									port@0 {
										reg = <0>;
										ecam_ar1335_out1: endpoint {
											port-index = <2>;
											bus-width = <CAM_LANES>;
											remote-endpoint = <&ecam_csi_in1>;
										};
									};
								};
							};
						};
						i2c@4{
							reg = <4>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ar1335_e@42 {
								status = "okay";
								compatible = "nvidia,ar1335";
								reg = <0x42>;
								sensor_model ="ar1335";
								avdd-reg = "vana";
								iovdd-reg = "vif";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
									 <&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								clock-frequency = <24000000>;
								reset-gpios = <&tca6424_22 4 GPIO_ACTIVE_HIGH>;
								pwdn-gpios =  <&tca6424_22 10 GPIO_ACTIVE_HIGH>;
								use_sensor_mode_id = "false";
								camera_mipi_lanes = <CAM_LANES>;
								pwm-upper_limit = <342>;
								pwm-lower_limit = <340>;
								pwm-invert= <1>;
								pwm-duty_ns = <0x1CBC720>;
								pwm-period_ns = <0x2045B80>;
								pwm-calib-gpio = <&gpio PWM_CALIB_GPIO GPIO_ACTIVE_HIGH>;
								pwm-name = "ecam-trigger";
								mode0 { // MODE_640X480
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_e";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";

									active_w = "640";
									active_h = "480";
									pixel_phase = "uyvy";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "300000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									step_gain_val = "1";
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "100";
									step_framerate = "1";
									default_framerate = "30000000";
									min_exp_time = "1";
									max_exp_time = "43000000";
								};
								mode1 { // MODE_1280X720
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_e";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";
									active_w = "1280";
									active_h = "720";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "72";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode2 { // MODE_1920X1080
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_e";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "1920";
									active_h = "1080";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1.5";
									max_framerate = "72";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode3 { // MODE_3840X2160
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_e";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "3840";
									active_h = "2160";
									readout_orientation = "0";
									dskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "30";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode4 { // MODE_4096X2160
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_e";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "4096";
									active_h = "2160";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "19";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode5 { // MODE_4192X3120
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_e";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "4192";
									active_h = "3120";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "1152000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "19";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ecam_ar1335_out2: endpoint {
											port-index = <4>;
											bus-width = <CAM_LANES>;
											remote-endpoint = <&ecam_csi_in2>;
										};
									};
								};
							};
						};
						i2c@5{
							reg = <5>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ar1335_g@42 {
								status = "okay";
								compatible = "nvidia,ar1335";
								reg = <0x42>;
								sensor_model ="ar1335";
								avdd-reg = "vana";
								iovdd-reg = "vif";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
									 <&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								clock-frequency = <24000000>;
								reset-gpios = <&tca6424_22 5 GPIO_ACTIVE_HIGH>;
								pwdn-gpios =  <&tca6424_22 11 GPIO_ACTIVE_HIGH>;
								use_sensor_mode_id = "false";
								camera_mipi_lanes = <CAM_LANES>;
								pwm-upper_limit = <342>;
								pwm-lower_limit = <340>;
								pwm-invert= <1>;
								pwm-duty_ns = <0x1CBC720>;
								pwm-period_ns = <0x2045B80>;
								pwm-calib-gpio = <&gpio PWM_CALIB_GPIO GPIO_ACTIVE_HIGH>;
								pwm-name = "ecam-trigger";
								mode0 { // MODE_640X480
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_g";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";

									active_w = "640";
									active_h = "480";
									pixel_phase = "uyvy";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "300000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									step_gain_val = "1";
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "100";
									step_framerate = "1";
									default_framerate = "30000000";
									min_exp_time = "1";
									max_exp_time = "43000000";
								};
								mode1 { // MODE_1280X720
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_g";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";
									active_w = "1280";
									active_h = "720";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "72";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode2 { // MODE_1920X1080
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_g";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "1920";
									active_h = "1080";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1.5";
									max_framerate = "72";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode3 { // MODE_3840X2160
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_g";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "3840";
									active_h = "2160";
									readout_orientation = "0";
									dskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "30";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode4 { // MODE_4096X2160
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_g";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "4096";
									active_h = "2160";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "350000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "19";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								mode5 { // MODE_4192X3120
									mclk_khz = "24000";
									num_lanes = CAM_LANES_STRING;
									tegra_sinterface = "serial_g";
									phy_mode = "DPHY";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "0";
									dynamic_pixel_bit_depth = "16";
									csi_pixel_bit_depth = "16";
									mode_type = "yuv";
									pixel_phase = "uyvy";

									active_w = "4192";
									active_h = "3120";
									readout_orientation = "0";
									deskew_initial_enable = "false";
									line_length = "2000";
									inherent_gain = "1";
									mclk_multiplier = "18";
									pix_clk_hz = "1152000000";

									min_gain_val = "0"; /* dB */
									max_gain_val = "48"; /* dB */
									default_gain = "0";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1";
									max_framerate = "19";
									default_framerate = "30";
									min_exp_time = "30";
									max_exp_time = "660000";
								};
								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										ecam_ar1335_out3: endpoint {
											port-index = <6>;
											bus-width = <CAM_LANES>;
										remote-endpoint = <&ecam_csi_in3>;
										};
									};
								};
							};
						};
					};
				};

				host1x@13e00000 {
					nvcsi@15a00000 {
						num-channels = <4>;
						#address-cells = <1>;
						#size-cells = <0>;
						csi_chan0: channel@0 {
							status = "okay";
							reg = <0>;
							ports {
								status = "okay";
								#address-cells = <1>;
								#size-cells = <0>;
								csi_chan0_port0: port@0 {
									status = "okay";
									reg = <0>;
									ecam_csi_in0: endpoint@0 {
										status = "okay";
										port-index = <0>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&ecam_ar1335_out0>;
									};
								};
								csi_chan0_port1: port@1 {
									status = "okay";
									reg = <1>;
									ecam_csi_out0: endpoint@1 {
										status = "okay";
										remote-endpoint = <&ecam_vi_in0>;
									};
								};
							};
						};
						csi_chan1: channel@1 {
							status = "okay";
							reg = <1>;
							ports {
								status = "okay";
								#address-cells = <1>;
								#size-cells = <0>;
								csi_chan1_port0: port@0 {
									status = "okay";
									reg = <0>;
									ecam_csi_in1: endpoint@2 {
										status = "okay";
										port-index = <2>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&ecam_ar1335_out1>;
									};
								};
								csi_chan1_port1: port@1 {
									status = "okay";
									reg = <1>;
									ecam_csi_out1: endpoint@3 {
										status = "okay";
										remote-endpoint = <&ecam_vi_in1>;
									};
								};
							};
						};
						csi_chan2: channel@2 {
							status = "okay";
							reg = <2>;
							ports {
								status = "okay";
								#address-cells = <1>;
								#size-cells = <0>;
								csi_chan2_port0: port@0 {
									status = "okay";
									reg = <0>;
									ecam_csi_in2: endpoint@4 {
										status = "okay";
										port-index = <4>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&ecam_ar1335_out2>;
									};
								};
								csi_chan2_port1: port@1 {
									status = "okay";
									reg = <1>;
									ecam_csi_out2: endpoint@5 {
										status = "okay";
										remote-endpoint = <&ecam_vi_in2>;
									};
								};
							};
						};
						csi_chan3: channel@3 {
							status = "okay";
							reg = <3>;
							ports {
								status = "okay";
								#address-cells = <1>;
								#size-cells = <0>;
								csi_chan3_port0: port@0 {
									status = "okay";
									reg = <0>;
									ecam_csi_in3: endpoint@6 {
										status = "okay";
										port-index = <6>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&ecam_ar1335_out3>;
									};
								};
								csi_chan3_port1: port@1 {
									reg = <1>;
									ecam_csi_out3: endpoint@7 {
										status = "okay";
										remote-endpoint = <&ecam_vi_in3>;
									};
								};
							};
						};
					};
				};
			};

			tegra-capture-vi {
				num-channels = <4>;
				#address-cells = <1>;
				#size-cells = <0>;
				ports {
					status = "okay";
					#address-cells = <1>;
					#size-cells = <0>;
					vi_port0: port@0 {
						status = "okay";
						reg = <0>;
						ecam_vi_in0: endpoint {
							status = "okay";
							port-index = <0>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&ecam_csi_out0>;
						};
					};
					vi_port1: port@1 {
						status = "okay";
						reg = <1>;
						ecam_vi_in1: endpoint {
							status = "okay";
							port-index = <2>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&ecam_csi_out1>;
						};
					};
					vi_port2: port@2 {
						status = "okay";
						reg = <2>;
						ecam_vi_in2: endpoint {
							status = "okay";
							port-index = <4>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&ecam_csi_out2>;
						};
					};
					vi_port3: port@3 {
						status = "okay";
						reg = <3>;
						ecam_vi_in3: endpoint {
							status = "okay";
							port-index = <5>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&ecam_csi_out3>;
						};
					};
				};
			};

			tegra-camera-platform {
				compatible = "nvidia, tegra-camera-platform";
				num_csi_lanes = <12>;
				max_lane_speed = <2500000>;
				min_bits_per_pixel = <16>;
				vi_peak_byte_per_pixel = <2>;
				vi_bw_margin_pct = <67>;

				modules {
					cam_module0: module0 {
						status = "okay";
						badge = "ecam_hexcu130-1";
						position = "rear1";
						orientation = "1";
						cam_module0_drivernode0: drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@70/i2c@0/ar1335_a@42";
						};
					};
					cam_module1: module1 {
						status = "okay";
						badge = "ecam_hexcu130-2";
						position = "rear2";
						orientation = "1";
						cam_module1_drivernode0: drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@70/i2c@2/ar1335_c@42";
						};
					};
					cam_module2: module2 {
						status = "okay";
						badge = "ecam_hexcu130-3";
						position = "rear3";
						orientation = "1";
						cam_module2_drivernode0: drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@70/i2c@4/ar1335_e@42";
						};
					};
					cam_module3: module3 {
						status = "okay";
						badge = "ecam_hexcu130-4";
						position = "rear4";
						orientation = "1";
						cam_module3_drivernode0: drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@70/i2c@5/ar1335_g@42";
						};
					};
				};
			};
		};
	};
};
