// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2017-2023, NVIDIA CORPORATION & AFFILIATES. All rights reserved.

#include "tegra234-camera-imx185-a00.dtsi"
#include <dt-bindings/clock/tegra234-clock.h>
#include <dt-bindings/gpio/tegra234-gpio.h>

#define CAM0_RST_L	TEGRA234_MAIN_GPIO(H, 3)

/* camera control gpio definitions */

/ {
	fragment-camera-imx185@0 {
		target-path = "/bus@0";
		__overlay__ {
			gpio@2200000 {
				camera-control-output-low {
					gpio-hog;
					output-low;
					gpios = <CAM0_RST_L 0>;
					label = "cam0-rst";
				};
			};

			i2c@3180000 {
				tca9546@70 {
					compatible = "nxp,pca9546";
					reg = <0x70>;
					#address-cells = <1>;
					#size-cells = <0>;
					skip_mux_detect = "yes";

					i2c@0 {
						reg = <0>;
						i2c-mux,deselect-on-exit;
						#address-cells = <1>;
						#size-cells = <0>;
						pca9570_a@24 {
							compatible = "nxp,pca9570";
							reg = <0x24>;
							channel = "a";
							drive_ic = "DRV8838";
						};

						imx185_a@1a {
							/* Define any required hw resources needed by driver */
							/* ie. clocks, io pins, power sources */
							clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
								<&bpmp TEGRA234_CLK_EXTPERIPH1>;
							clock-names = "extperiph1", "pllp_grtba";
							mclk = "extperiph1";
							reset-gpios = <&gpio CAM0_RST_L GPIO_ACTIVE_HIGH>;
						};
					};
				};
			};
		};
	};
};
