// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2017-2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

/ {
	fragment-camera@0 {
		target-path = "/";
		__overlay__ {
			tegra-capture-vi {
				num-channels = <1>;
				ports {
					#address-cells = <1>;
					#size-cells = <0>;
					port@0 {
						reg = <0>;
						e3331_vi_in0: endpoint {
							port-index = <0>;
							bus-width = <3>;
							remote-endpoint = <&e3331_csi_out0>;
						};
					};
				};
			};

			bus@0 {
				host1x@13e00000 {
					nvcsi@15a00000 {
						num-channels = <1>;
						channel@0 {
							reg = <0>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									e3331_csi_in0: endpoint@0 {
										port-index = <0>;
										bus-width = <3>;
										remote-endpoint = <&e3331_imx318_out0>;
									};
								};
								port@1 {
									reg = <1>;
									e3331_csi_out0: endpoint@1 {
										remote-endpoint = <&e3331_vi_in0>;
									};
								};
							};
						};
					};
				};

				i2c@3180000 {
					tca9546@70 {
						i2c@0 {
							imx318_a@10 {
								compatible = "sony,imx318";
								reg = <0x10>;

								/* Physical dimensions of sensor */
								physical_w = "6.811";
								physical_h = "5.254";

								sensor_model = "imx318";
								/* Define any required hw resources needed by driver */
								/* ie. clocks, io pins, power sources */
								avdd-reg = "vana";
								iovdd-reg = "vif";
								dvdd-reg = "vdig";

								has-eeprom;

								/**
								* ==== Modes ====
								* A modeX node is required to support v4l2 driver
								* implementation with NVIDIA camera software stack
								*
								* == Signal properties ==
								*
								* phy_mode = "";
								* PHY mode used by the MIPI lanes for this device
								*
								* tegra_sinterface = "";
								* CSI Serial interface connected to tegra
								* Incase of virtual HW devices, use virtual
								* For SW emulated devices, use host
								*
								* pix_clk_hz = "";
								* Sensor pixel clock used for calculations like exposure and framerate
								*
								* readout_orientation = "0";
								* Based on camera module orientation.
								* Only change readout_orientation if you specifically
								* Program a different readout order for this mode
								*
								* == Image format Properties ==
								*
								* active_w = "";
								* Pixel active region width
								*
								* active_h = "";
								* Pixel active region height
								*
								* pixel_t = "";
								* The sensor readout pixel pattern
								*
								* line_length = "";
								* Pixel line length (width) for sensor mode.
								*
								* == Source Control Settings ==
								*
								* Gain factor used to convert fixed point integer to float
								* Gain range [min_gain/gain_factor, max_gain/gain_factor]
								* Gain step [step_gain/gain_factor is the smallest step that can be configured]
								* Default gain [Default gain to be initialized for the control.
								*     use min_gain_val as default for optimal results]
								* Framerate factor used to convert fixed point integer to float
								* Framerate range [min_framerate/framerate_factor, max_framerate/framerate_factor]
								* Framerate step [step_framerate/framerate_factor is the smallest step that can be configured]
								* Default Framerate [Default framerate to be initialized for the control.
								*     use max_framerate to get required performance]
								* Exposure factor used to convert fixed point integer to float
								* For convenience use 1 sec = 1000000us as conversion factor
								* Exposure range [min_exp_time/exposure_factor, max_exp_time/exposure_factor]
								* Exposure step [step_exp_time/exposure_factor is the smallest step that can be configured]
								* Default Exposure Time [Default exposure to be initialized for the control.
								*     Set default exposure based on the default_framerate for optimal exposure settings]
								*
								* gain_factor = ""; (integer factor used for floating to fixed point conversion)
								* min_gain_val = ""; (ceil to integer)
								* max_gain_val = ""; (ceil to integer)
								* step_gain_val = ""; (ceil to integer)
								* default_gain = ""; (ceil to integer)
								* Gain limits for mode
								*
								* exposure_factor = ""; (integer factor used for floating to fixed point conversion)
								* min_exp_time = ""; (ceil to integer)
								* max_exp_time = ""; (ceil to integer)
								* step_exp_time = ""; (ceil to integer)
								* default_exp_time = ""; (ceil to integer)
								* Exposure Time limits for mode (sec)
								*
								* framerate_factor = ""; (integer factor used for floating to fixed point conversion)
								* min_framerate = ""; (ceil to integer)
								* max_framerate = ""; (ceil to integer)
								* step_framerate = ""; (ceil to integer)
								* default_framerate = ""; (ceil to integer)
								* Framerate limits for mode (fps)
								*
								* embedded_metadata_height = "";
								* Sensor embedded metadata height in units of rows.
								* If sensor does not support embedded metadata value should be 0.
								*/
								mode0 { /*IMX318_MODE_5488X4112_30FPS*/
									mclk_khz = "24000";
									num_lanes = "3";
									phy_mode = "CPHY";
									tegra_sinterface = "serial_a";
									discontinuous_clk = "no";
									dpcm_enable = "false";
									cil_settletime = "30";

									active_w = "5488";
									active_h = "4112";
									mode_type = "bayer";
									pixel_phase = "bggr";
									csi_pixel_bit_depth = "10";
									readout_orientation = "0";
									line_length = "5488";
									inherent_gain = "1";
									mclk_multiplier = "31.25";
									pix_clk_hz = "750000000";

									gain_factor = "16";
									framerate_factor = "1000000";
									exposure_factor = "1000000";
									min_gain_val = "16"; /* 1.0 */
									max_gain_val = "256"; /* 16.0 */
									step_gain_val = "1"; /* 0.125 */
									default_gain = "16";
									min_hdr_ratio = "1";
									max_hdr_ratio = "1";
									min_framerate = "1500000"; /* 1.5 */
									max_framerate = "30000000"; /* 30 */
									step_framerate = "1";
									default_framerate= "30000000";
									min_exp_time = "34"; /* us */
									max_exp_time = "550385"; /* us */
									step_exp_time = "1";
									default_exp_time = "33334";/* us */
									embedded_metadata_height = "0";
								};

								ports {
									#address-cells = <1>;
									#size-cells = <0>;
									port@0 {
										reg = <0>;
										e3331_imx318_out0: endpoint {
											port-index = <0>;
											bus-width = <3>;
											remote-endpoint = <&e3331_csi_in0>;
										};
									};
								};
							};
						};
					};
				};
			};

			tegra-camera-platform {
				compatible = "nvidia, tegra-camera-platform";
				/**
				* Physical settings to calculate max ISO BW
				*
				* num_csi_lanes = <>;
				* Total number of CSI lanes when all cameras are active
				*
				* max_lane_speed = <>;
				* Max lane speed in Kbit/s
				*
				* min_bits_per_pixel = <>;
				* Min bits per pixel
				*
				* vi_peak_byte_per_pixel = <>;
				* Max byte per pixel for the VI ISO case
				*
				* vi_bw_margin_pct = <>;
				* Vi bandwidth margin in percentage
				*
				* max_pixel_rate = <>;
				* Max pixel rate in Kpixel/s for the ISP ISO case
				* Set this to the highest pix_clk_hz out of all available modes.
				*
				* isp_peak_byte_per_pixel = <>;
				* Max byte per pixel for the ISP ISO case
				*
				* isp_bw_margin_pct = <>;
				* Isp bandwidth margin in percentage
				*/
				num_csi_lanes = <3>;
				max_lane_speed = <1500000>;
				min_bits_per_pixel = <10>;
				vi_peak_byte_per_pixel = <2>;
				vi_bw_margin_pct = <25>;
				max_pixel_rate = <800000>;
				isp_peak_byte_per_pixel = <5>;
				isp_bw_margin_pct = <25>;

				/**
				* The general guideline for naming badge_info contains 3 parts, and is as follows,
				* The first part is the camera_board_id for the module; if the module is in a FFD
				* platform, then use the platform name for this part.
				* The second part contains the position of the module, ex. “rear” or “front”.
				* The third part contains the last 6 characters of a part number which is found
				* in the module's specsheet from the vender.
				*/
				modules {
					module0 {
						badge = "e3331_rear_22N02A";
						position = "rear";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@0/imx318_a@10";
						};
					};
				};
			};
		};
	};
};
