// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2018-2024, NVIDIA CORPORATION & AFFILIATES. All rights reserved.

/dts-v1/;
/plugin/;

#include <dt-bindings/clock/tegra234-clock.h>
#include <dt-bindings/gpio/tegra234-gpio.h>
#include <dt-bindings/tegra234-p3737-0000+p3701-0000.h>

#define CAM0_RST_L	TEGRA234_MAIN_GPIO(H, 3)
#define CAM0_PWDN	TEGRA234_MAIN_GPIO(H, 6)
#define CAM1_RST_L	TEGRA234_MAIN_GPIO(AC, 1)
#define CAM1_PWDN	TEGRA234_MAIN_GPIO(AC, 0)
#define PWR_EN		TEGRA234_MAIN_GPIO(AC, 7)
#define GYRO1_IRQ_GPIO	TEGRA234_AON_GPIO(BB, 1)
#define ACCE1_IRQ_GPIO	TEGRA234_A<PERSON>_GPIO(BB, 0)

/* camera control gpio definitions */
/ {
	overlay-name = "Jetson Camera e3653-dual-Hawk module";
	jetson-header-name = "Jetson AGX CSI Connector";
	compatible = JETSON_COMPATIBLE;

	fragment@0 {
		target-path = "/";
		__overlay__ {
			tegra-capture-vi {
				num-channels = <4>;
				ports {
					status = "okay";
					port@0 {
						status = "okay";
						dual_hawk_vi_in0: endpoint {
							status = "okay";
							vc-id = <0>;
							port-index = <0>;
							bus-width = <2>;
							remote-endpoint = <&dual_hawk_csi_out0>;
						};
					};
					port@1 {
						status = "okay";
						dual_hawk_vi_in1: endpoint {
							status = "okay";
							vc-id = <1>;
							port-index = <0>;
							bus-width = <2>;
							remote-endpoint = <&dual_hawk_csi_out1>;
						};
					};
					port@2 {
						status = "okay";
						dual_hawk_vi_in2: endpoint {
							status = "okay";
							vc-id = <0>;
							port-index = <1>;
							bus-width = <2>;
							remote-endpoint = <&dual_hawk_csi_out2>;
						};
					};
					port@3 {
						status = "okay";
						dual_hawk_vi_in3: endpoint {
							status = "okay";
							vc-id = <1>;
							port-index = <1>;
							bus-width = <2>;
							remote-endpoint = <&dual_hawk_csi_out3>;
						};
					};
				};
			};
			tegra-camera-platform {
				modules {
					status = "okay";
					module0 {
						status = "okay";
						badge = "dual_hawk_bottomleft";
						position = "bottomleft";
						orientation = "1";
						drivernode0 {
							status = "okay";
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_sensor";
							/* Declare the device-tree hierarchy to driver instance */
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@0/dual_hawk_a@18";
						};
					};
					module1 {
						status = "okay";
						badge = "dual_hawk_bottomright";
						position = "bottomright";
						orientation = "1";
						drivernode0 {
							status = "okay";
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_sensor";
							/* Declare the device-tree hierarchy to driver instance */
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@0/dual_hawk_b@10";
						};
					};
					module2 {
						badge = "dual_hawk_centerleft";
						position = "centerleft";
						orientation = "1";
						status = "okay";
						drivernode0 {
							status = "okay";
							/* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_sensor";
							/* Declare the device-tree hierarchy to driver instance */
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@1/dual_hawk_c@18";
						};
					};
					module3 {
						badge = "dual_hawk_centerright";
						position = "centerright";
						orientation = "1";
						status = "okay";
						drivernode0 {
							status = "okay";
							 /* Declare PCL support driver (classically known as guid)  */
							pcl_id = "v4l2_sensor";
							/* Declare the device-tree hierarchy to driver instance */
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@1/dual_hawk_d@10";
						};
					};
				};
			};
			bus@0{
				host1x@13e00000 {
					nvcsi@15a00000 {
						num-channels = <4>;
						channel@0 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									dual_hawk_csi_in0: endpoint@0 {
										status = "okay";
										port-index = <0>;
										bus-width = <2>;
										remote-endpoint = <&dual_hawk_out0>;
									};
								};
								port@1 {
									status = "okay";
									dual_hawk_csi_out0: endpoint@1 {
										status = "okay";
										remote-endpoint = <&dual_hawk_vi_in0>;
									};
								};
							};
						};
						channel@1 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									dual_hawk_csi_in1: endpoint@2 {
										port-index = <0>;
										bus-width = <2>;
										remote-endpoint = <&dual_hawk_out1>;
									};
								};
								port@1 {
									status = "okay";
									dual_hawk_csi_out1: endpoint@3 {
										status = "okay";
										remote-endpoint = <&dual_hawk_vi_in1>;
									};
								};
							};
						};
						channel@2 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									dual_hawk_csi_in2: endpoint@4 {
										status = "okay";
										port-index = <1>;
										bus-width = <2>;
										remote-endpoint = <&dual_hawk_out2>;
									};
								};
								port@1 {
									status = "okay";
									dual_hawk_csi_out2: endpoint@5 {
										status = "okay";
										remote-endpoint = <&dual_hawk_vi_in2>;
									};
								};
							};
						};
						channel@3 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									dual_hawk_csi_in3: endpoint@6 {
										status = "okay";
										port-index = <1>;
										bus-width = <2>;
										remote-endpoint = <&dual_hawk_out3>;
									};
								};
								port@1 {
									status = "okay";
									dual_hawk_csi_out3: endpoint@7 {
										status = "okay";
										remote-endpoint = <&dual_hawk_vi_in3>;
									};
								};
							};
						};
					};
				};
				i2c@3180000 {
					tca9546@70 {
						compatible = "nxp,pca9546";
						reg = <0x70>;
						#address-cells = <1>;
						#size-cells = <0>;
						skip_mux_detect = "yes";
						status = "okay";
						i2c@0 {
							reg = <0>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							single_max96712_a@62 {
								status = "okay";
								compatible = "nvidia,max96712";
								reg = <0x62>;
								channel = "a";
							};
							dual_hawk_a@18 {
								status = "okay";
								def-addr = <0x18>;
								/* Define any required hw resources needed by driver */
								/* ie. clocks, io pins, power sources */
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
									<&bpmp TEGRA234_CLK_EXTPERIPH1>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								channel = "a";
								has-eeprom;
								reset-gpios = <&gpio CAM0_RST_L GPIO_ACTIVE_HIGH>;
								pwdn-gpios = <&gpio CAM1_PWDN GPIO_ACTIVE_HIGH>;
								pwr-gpios = <&gpio PWR_EN GPIO_ACTIVE_HIGH>;
								ports {
									port@0 {
										dual_hawk_out0: endpoint {
											vc-id = <0>;
											port-index = <0>;
											bus-width = <2>;
											remote-endpoint = <&dual_hawk_csi_in0>;
										};
									};
								};
							};
							dual_hawk_b@10 {
								status = "okay";
								def-addr = <0x10>;
								/* Define any required hw resources needed by driver */
								/* ie. clocks, io pins, power sources */
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
									<&bpmp TEGRA234_CLK_EXTPERIPH1>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								channel = "n";
								has-eeprom;
								reset-gpios = <&gpio CAM0_RST_L GPIO_ACTIVE_HIGH>;
								pwdn-gpios = <&gpio CAM1_PWDN GPIO_ACTIVE_HIGH>;
								pwr-gpios = <&gpio PWR_EN GPIO_ACTIVE_HIGH>;
								ports {
										port@0 {
											dual_hawk_out1: endpoint {
											vc-id = <1>;
											port-index = <0>;
											bus-width = <2>;
											remote-endpoint = <&dual_hawk_csi_in1>;
										};
									};	
								};
							};
							bmi088_a@69 {
								status = "okay";
								compatible = "bmi,bmi088";
								reg = <0x69>;
								accel_i2c_addr = <0x19>;
								/* Old BMI088 driver uses *_gpio property and the latest
								 * BMI088 driver uses *-gpios property. Have both versions
								 * to maintain backward compatibility.
								 */
								accel_irq_gpio = <&gpio_aon ACCE1_IRQ_GPIO GPIO_ACTIVE_HIGH>;
								gyro_irq_gpio = <&gpio_aon GYRO1_IRQ_GPIO GPIO_ACTIVE_HIGH>;
								accel_irq-gpios = <&gpio_aon ACCE1_IRQ_GPIO GPIO_ACTIVE_HIGH>;
								gyro_irq-gpios = <&gpio_aon GYRO1_IRQ_GPIO GPIO_ACTIVE_HIGH>;
								accel_matrix = [01 00 00 00 01 00 00 00 01];
								gyro_matrix = [01 00 00 00 01 00 00 00 01];
								gyro_reg_0x18 = <0x81>;
								timestamps = <&hte_aon ACCE1_IRQ_GPIO>, <&hte_aon GYRO1_IRQ_GPIO>;
								timestamp-names = "accelerometer", "gyroscope";
							};
						};
						i2c@1 {
							reg = <1>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							dual_hawk_c@18 {
								status = "okay";
								def-addr = <0x18>;
								/* Define any required hw resources needed by driver */
								/* ie. clocks, io pins, power sources */
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
									<&bpmp TEGRA234_CLK_EXTPERIPH1>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								channel = "n";
								has-eeprom;
								reset-gpios = <&gpio CAM0_RST_L GPIO_ACTIVE_HIGH>;
								pwdn-gpios = <&gpio CAM1_PWDN GPIO_ACTIVE_HIGH>;
								pwr-gpios = <&gpio PWR_EN GPIO_ACTIVE_HIGH>;
								ports {
									port@0 {
										dual_hawk_out2: endpoint {
											vc-id = <0>;
											port-index = <1>;
											bus-width = <2>;
											remote-endpoint = <&dual_hawk_csi_in2>;
										};
									};
								};
							};
							dual_hawk_d@10 {
								status = "okay";
								def-addr = <0x10>;
								/* Define any required hw resources needed by driver */
								/* ie. clocks, io pins, power sources */
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
									<&bpmp TEGRA234_CLK_EXTPERIPH1>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								channel = "n";
								has-eeprom;
								reset-gpios = <&gpio CAM0_RST_L GPIO_ACTIVE_HIGH>;
								pwdn-gpios = <&gpio CAM1_PWDN GPIO_ACTIVE_HIGH>;
								pwr-gpios = <&gpio PWR_EN GPIO_ACTIVE_HIGH>;
								ports {
									port@0 {
										dual_hawk_out3: endpoint {
											vc-id = <1>;
											port-index = <1>;
											bus-width = <2>;
											remote-endpoint = <&dual_hawk_csi_in3>;
										};
									};
								};
							};
						};
					};		
				};
			};
			nvpps {
				status = "disabled";
				compatible = "nvidia,tegra194-nvpps";
				interface = "eth0";
				sec_interface = "eth0";
			};
		};
	};
};
