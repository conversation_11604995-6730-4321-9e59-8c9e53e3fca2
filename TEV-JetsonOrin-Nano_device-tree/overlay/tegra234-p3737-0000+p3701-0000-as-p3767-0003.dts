// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2023-2024, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

/dts-v1/;
/plugin/;

/ {
	overlay-name = "Tegra234 p3701-0000-as-p3767-0003 Emulation Overlay";

	fragment-t234-p3701-0000-as-p3767-0003@0 {
		target-path = "/";
		board_config {
			ids = "3701-0000-*", "3701-0005-*";
		};
		__overlay__ {
			compatible = "nvidia,p3737-0000+p3701-0000-as-p3767-0003", "nvidia,tegra234";
			model = "Jetson AGX Orin as Nano 8GB";
			bus@0 {
				host1x@13e00000 {
					nvdla0@15880000 {
						status = "disabled";
					};
					nvdla1@158c0000 {
						status = "disabled";
					};
					pva0@16000000 {
						status = "disabled";
					};
					nvenc@154c0000 {
						status = "disabled";
					};
				};
				/* C1 */
				pcie@14100000 {
					max-link-speed = <0x3>;
				};
				/* C4 */
				pcie@14160000 {
					max-link-speed = <0x3>;
				};
				/* C4 End Point */
				pcie-ep@14160000 {
					max-link-speed = <0x3>;
				};
				/* C7 */
				pcie@141e0000 {
					max-link-speed = <0x3>;
				};
				/* C8 */
				pcie@140a0000 {
					max-link-speed = <0x3>;
				};
				/* C9 */
				pcie@140c0000 {
					max-link-speed = <0x3>;
				};
			};
			opp-table-cluster0 {
				opp-1510400000 { /* Max CPU freq for Orin Nano */
					opp-hz = /bits/ 64 <1510400000>;
					opp-peak-kBps = <3200000>;
				};
			};
			opp-table-cluster1 {
				opp-1510400000 { /* Max CPU freq for Orin Nano */
					opp-hz = /bits/ 64 <1510400000>;
					opp-peak-kBps = <3200000>;
				};
			};
			opp-table-cluster2 {
				opp-1510400000 { /* Max CPU freq for Orin Nano */
					opp-hz = /bits/ 64 <1510400000>;
					opp-peak-kBps = <3200000>;
				};
			};
		};
	};
};
