// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2021-2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.
/*
 * Device-tree overlay for tegra234-p3737-0000-p3701-0000 40-pin
 * Expansion Header.
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/pinctrl/pinctrl-tegra.h>
#include <dt-bindings/tegra234-p3737-0000+p3701-0000.h>

/ {
	overlay-name = "Jetson 40pin Header";
	compatible = JETSON_COMPATIBLE;

	p3737-0000_p3701-0000-hdr40@0 {
		target = <&pinmux>;
		__overlay__ {
			pinctrl-names = "default";
			pinctrl-0 = <&jetson_io_pinmux>;
			jetson_io_pinmux: exp-header-pinmux {
				hdr40-pin7 {
					nvidia,pins = "soc_gpio33_pq6";
					nvidia,function = "extperiph4";
					nvidia,pin-group = "extperiph4_clk";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin8 {
					nvidia,pins = "uart1_tx_pr2";
				};
				hdr40-pin10 {
					nvidia,pins = "uart1_rx_pr3";
				};
				hdr40-pin11 {
					nvidia,pins = "uart1_rts_pr4";
					nvidia,function = "uarta";
					nvidia,pin-group = "uarta-cts/rts";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin12 {
					nvidia,pins = "soc_gpio41_ph7";
					nvidia,function = "i2s2";
					nvidia,pin-label = "i2s2_sclk";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin13 {
					nvidia,pins = "soc_gpio37_pr0";
					nvidia,function = "gp";
					nvidia,pin-group = "pwm8";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin15 {
					nvidia,pins = "soc_gpio39_pn1";
					nvidia,function = "gp";
					nvidia,pin-group = "pwm1";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin18 {
					nvidia,pins = "soc_gpio21_ph0";
					nvidia,function = "gp";
					nvidia,pin-group = "pwm5";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin19 {
					nvidia,pins = "spi1_mosi_pz5";
					nvidia,function = "spi1";
					nvidia,pin-label = "spi1_dout";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin21 {
					nvidia,pins = "spi1_miso_pz4";
					nvidia,function = "spi1";
					nvidia,pin-label = "spi1_din";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin23 {
					nvidia,pins = "spi1_sck_pz3";
					nvidia,function = "spi1";
					nvidia,pin-label = "spi1_sck";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin24 {
					nvidia,pins = "spi1_cs0_pz6";
					nvidia,function = "spi1";
					nvidia,pin-label = "spi1_cs0";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin26 {
					nvidia,pins = "spi1_cs1_pz7";
					nvidia,function = "spi1";
					nvidia,pin-label = "spi1_cs1";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin35 {
					nvidia,pins = "soc_gpio44_pi2";
					nvidia,function = "i2s2";
					nvidia,pin-label = "i2s2_fs";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin36 {
					nvidia,pins = "uart1_cts_pr5";
					nvidia,function = "uarta";
					nvidia,pin-group = "uarta-cts/rts";
					nvidia,tristate = <TEGRA_PIN_ENABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin38 {
					nvidia,pins = "soc_gpio43_pi1";
					nvidia,function = "i2s2";
					nvidia,pin-label = "i2s2_din";
					nvidia,tristate = <TEGRA_PIN_ENABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin40 {
					nvidia,pins = "soc_gpio42_pi0";
					nvidia,function = "i2s2";
					nvidia,pin-label = "i2s2_dout";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
			};
		};
	};

	fragment@1 {
		target = <&pinmux_aon>;
		__overlay__ {
			pinctrl-names = "default";
			pinctrl-0 = <&jetson_io_pinmux_aon>;
			jetson_io_pinmux_aon: exp-header-pinmux {
				hdr40-pin3 {
					nvidia,pins = "gen8_i2c_scl_pdd1";
					nvidia,pin-label = "i2c8";
				};
				hdr40-pin5 {
					nvidia,pins = "gen8_i2c_sda_pdd2";
					nvidia,pin-label = "i2c8";
				};
				hdr40-pin16a {
					nvidia,pins = "can1_en_pbb1";
					nvidia,function = "dmic3";
					nvidia,pin-label = "dmic3_dat";
					nvidia,tristate = <TEGRA_PIN_ENABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin16b {
					nvidia,pins = "can1_en_pbb1";
					nvidia,function = "dmic5";
					nvidia,pin-label = "dmic5_dat";
					nvidia,tristate = <TEGRA_PIN_ENABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin27 {
					nvidia,pins = "gen2_i2c_sda_pdd0";
				};
				hdr40-pin28 {
					nvidia,pins = "gen2_i2c_scl_pcc7";
				};
				hdr40-pin29 {
					nvidia,pins = "can0_din_paa1";
					nvidia,function = "can0";
					nvidia,pin-label = "can0_din";
					nvidia,tristate = <TEGRA_PIN_ENABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
				hdr40-pin31 {
					nvidia,pins = "can0_dout_paa0";
					nvidia,function = "can0";
					nvidia,pin-label = "can0_dout";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin32a {
					nvidia,pins = "can1_stb_pbb0";
					nvidia,function = "dmic3";
					nvidia,pin-label = "dmic3_clk";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin32b {
					nvidia,pins = "can1_stb_pbb0";
					nvidia,function = "dmic5";
					nvidia,pin-label = "dmic5_clk";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin33 {
					nvidia,pins = "can1_dout_paa2";
					nvidia,function = "can1";
					nvidia,pin-label = "can1_dout";
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				};
				hdr40-pin37 {
					nvidia,pins = "can1_din_paa3";
					nvidia,function = "can1";
					nvidia,pin-label = "can1_din";
					nvidia,tristate = <TEGRA_PIN_ENABLE>;
					nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				};
			};
		};
	};
};
