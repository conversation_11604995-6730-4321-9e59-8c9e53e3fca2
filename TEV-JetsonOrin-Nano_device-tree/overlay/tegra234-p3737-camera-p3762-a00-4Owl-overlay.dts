// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2024, NVIDIA CORPORATION & AFFILIATES. All rights reserved.

/dts-v1/;
/plugin/;

#include "tegra234-p3737-camera-p3762-a00.dtsi"
#include "dt-bindings/gpio/tegra234-gpio.h"
#include "dt-bindings/clock/tegra234-clock.h"
#include <dt-bindings/tegra234-p3737-0000+p3701-0000.h>

/* camera control gpio definitions */
#define CAM0_RST_L      TEGRA234_MAIN_GPIO(H, 3)
#define CAM0_PWDN       TEGRA234_MAIN_GPIO(H, 6)
#define CAM1_RST_L      TEGRA234_MAIN_GPIO(AC, 1)
#define CAM1_PWDN       TEGRA234_MAIN_GPIO(AC, 0)
#define PWR_EN      TEGRA234_MAIN_GPIO(AC, 7)
#define GYRO1_IRQ_GPIO  TEGRA234_AON_GPIO(CC, 1)
#define ACCE1_IRQ_GPIO  TEGRA234_AON_GPIO(CC, 0)

/ {
	overlay-name = "Jetson Camera 4-Owl p3762 module";
	jetson-header-name = "Jetson AGX CSI Connector";
	compatible = JETSON_COMPATIBLE;

	fragment-camera-hawk-owl@0 {
		target-path = "/";
		__overlay__ {
			tegra-capture-vi {
				num-channels = <12>;
				ports {
					#address-cells = <1>;
					#size-cells = <0>;
					port@0 {
						status = "disabled";
					};
					port@1 {
						status = "disabled";
					};
					port@2 {
						status = "disabled";
					};
					port@3 {
						status = "disabled";
					};
					port@4 {
						status = "disabled";
					};
					port@5 {
						status = "disabled";
					};
					port@6 {
						status = "disabled";
					};
					port@7 {
						status = "disabled";
					};
					port@8 {
						status = "okay";
					};
					port@9 {
						status = "okay";
					};
					port@10 {
						status = "okay";
					};
					port@11 {
						status = "okay";
					};
				};
			};
			bus@0 {
				host1x@13e00000 {
					nvcsi@15a00000 {
						num-channels = <12>;
						#address-cells = <1>;
						#size-cells = <0>;
						channel@0 {
							status = "disabled";
						};
						channel@1 {
							status = "disabled";
						};
						channel@2 {
							status = "disabled";
						};
						channel@3 {
							status = "disabled";
						};
						channel@4 {
							status = "disabled";
						};
						channel@5 {
							status = "disabled";
						};
						channel@6 {
							status = "disabled";
						};
						channel@7 {
							status = "disabled";
						};
						channel@8 {
							status = "okay";
						};
						channel@9 {
							status = "okay";
						};
						channel@10 {
							status = "okay";
						};
						channel@11 {
							status = "okay";
						};
					};
				};
				i2c@3180000 {
					max96712_b@62 {
						status = "okay";
					};
					ar0234_i@30 {
						status = "okay";
					};
					ar0234_j@32 {
						status = "okay";
					};
					ar0234_k@34 {
						status = "okay";
					};
					ar0234_l@36 {
						status = "okay";
					};
				};
				i2c@31e0000 {
					max96712_a@62 {
						status = "disabled";
					};
					virtual_i2c_mux@50 {
						status = "disabled";

						i2c@0 {
							bmi088_a@69 {
								status = "disabled";
							};
							ar0234_a@30 {
								status = "disabled";
							};
							ar0234_b@31 {
								status = "disabled";
							};
						};
						i2c@1 {
							ar0234_c@32 {
								status = "disabled";
							};
							ar0234_d@33 {
								status = "disabled";
							};
							ar0234_e@34 {
								status = "disabled";
							};
							ar0234_f@35 {
								status = "disabled";
							};
							ar0234_g@36 {
								status = "disabled";
							};
							ar0234_h@37 {
								status = "disabled";
							};
						};
					};
				};
			};
		};
	};
};
