// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2015-2024, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

/dts-v1/;
/plugin/;

#include <dt-bindings/clock/tegra234-clock.h>
#include <dt-bindings/gpio/tegra234-gpio.h>
#include <dt-bindings/tegra234-p3737-0000+p3701-0000.h>

#define CAM0_RST_L	TEGRA234_MAIN_GPIO(H, 3)
#define CAM0_PWDN	TEGRA234_MAIN_GPIO(H, 6)
#define CAM1_RST_L	TEGRA234_MAIN_GPIO(AC, 1)
#define CAM1_PWDN	TEGRA234_MAIN_GPIO(AC, 0)

/ {
	overlay-name = "Jetson Camera E3333 module";
	jetson-header-name = "Jetson AGX CSI Connector";
	compatible = JETSON_COMPATIBLE;

	fragment@0 {
		target-path = "/";

		board_config {
			ids = "3333-*";
			sw-modules = "kernel";
		};

		__overlay__ {
			tegra-capture-vi {
				num-channels = <6>;
				ports {
					status = "okay";
					port@0 {
						status = "okay";
						endpoint {
							status = "okay";
							port-index = <0>;
							bus-width = <2>;
							remote-endpoint = <&e3333_csi_out0>;
						};
					};
					port@1 {
						status = "okay";
						endpoint {
							status = "okay";
							port-index = <1>;
							bus-width = <2>;
							remote-endpoint = <&e3333_csi_out1>;
						};
					};
					port@2 {
						status = "okay";
						endpoint {
							status = "okay";
							port-index = <2>;
							bus-width = <2>;
							remote-endpoint = <&e3333_csi_out2>;
						};
					};
					port@3 {
						status = "okay";
						endpoint {
							status = "okay";
							port-index = <3>;
							bus-width = <2>;
							remote-endpoint = <&e3333_csi_out3>;
						};
					};
					port@4 {
						status = "okay";
						endpoint {
							status = "okay";
							port-index = <4>;
							bus-width = <2>;
							remote-endpoint = <&e3333_csi_out4>;
						};
					};
					port@5 {
						status = "okay";
						endpoint {
							status = "okay";
							port-index = <5>;
							bus-width = <2>;
							remote-endpoint = <&e3333_csi_out5>;
						};
					};
				};
			};
			tegra-camera-platform {
				compatible = "nvidia, tegra-camera-platform";
				modules {
					module0 {
						status = "okay";
						badge = "e3333_bottomleft_P5V27C";
						position = "bottomleft";
						orientation = "1";
						drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@77/i2c@0/ov5693_a@36";
						};
						drivernode1 {
							status = "okay";
							pcl_id = "v4l2_lens";
							sysfs-device-tree = "/sys/firmware/devicetree/base/e3333_lens_ov5693@P5V27C/";
						};
					};
					module1 {
						status = "okay";
						badge = "e3333_centerleft_P5V27C";
						position = "centerleft";
						orientation = "1";
						drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@77/i2c@1/ov5693_b@36";
						};
						drivernode1 {
							status = "okay";
							pcl_id = "v4l2_lens";
							sysfs-device-tree = "/sys/firmware/devicetree/base/e3333_lens_ov5693@P5V27C/";
						};
					};
					module2 {
						status = "okay";
						badge = "e3333_centerright_P5V27C";
						position = "centerright";
						orientation = "1";
						drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@77/i2c@2/ov5693_c@36";
						};
						drivernode1 {
							status = "okay";
							pcl_id = "v4l2_lens";
							sysfs-device-tree = "/sys/firmware/devicetree/base/e3333_lens_ov5693@P5V27C/";
						};
					};
					module3 {
						status = "okay";
						badge = "e3333_topleft_P5V27C";
						position = "topleft";
						orientation = "1";
						drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@77/i2c@3/ov5693_d@36";
						};
						drivernode1 {
							status = "okay";
							pcl_id = "v4l2_lens";
							sysfs-device-tree = "/sys/firmware/devicetree/base/e3333_lens_ov5693@P5V27C/";
						};
					};
					module4 {
						status = "okay";
						badge = "e3333_bottomright_P5V27C";
						position = "bottomright";
						orientation = "1";
						drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@77/i2c@4/ov5693_e@36";
						};
						drivernode1 {
							status = "okay";
							pcl_id = "v4l2_lens";
							sysfs-device-tree = "/sys/firmware/devicetree/base/e3333_lens_ov5693@P5V27C/";
						};
					};
					module5 {
						status = "okay";
						badge = "e3333_topright_P5V27C";
						position = "topright";
						orientation = "1";
						drivernode0 {
							status = "okay";
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9548@77/i2c@5/ov5693_g@36";
						};
						drivernode1 {
							status = "okay";
							pcl_id = "v4l2_lens";
							sysfs-device-tree = "/sys/firmware/devicetree/base/e3333_lens_ov5693@P5V27C/";
						};
					};
				};
			};
			e3333_lens_ov5693@P5V27C {
				status = "okay";
				min_focus_distance = "0.0";
				hyper_focal = "0.0";
				focal_length = "2.67";
				f_number = "2.0";
				aperture = "2.0";
			};
			bus@0 {
				host1x@13e00000 {
					nvcsi@15a00000 {
						num-channels = <6>;
						status = "okay";
						channel@0 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									endpoint@0 {
										status = "okay";
										port-index = <0>;
										bus-width = <2>;
										remote-endpoint = <&e3333_ov5693_out0>;
									};
								};
								port@1 {
									status = "okay";
									endpoint@1 {
										status = "okay";
										remote-endpoint = <&e3333_vi_in0>;
									};
								};
							};
						};
						channel@1 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									endpoint@2 {
										status = "okay";
										port-index = <1>;
										bus-width = <2>;
										remote-endpoint = <&e3333_ov5693_out1>;
									};
								};
								port@1 {
									status = "okay";
									endpoint@3 {
										status = "okay";
										remote-endpoint = <&e3333_vi_in1>;
									};
								};
							};
						};
						channel@2 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									endpoint@4 {
										status = "okay";
										port-index = <2>;
										bus-width = <2>;
										remote-endpoint = <&e3333_ov5693_out2>;
									};
								};
								port@1 {
									status = "okay";
									endpoint@5 {
										status = "okay";
										remote-endpoint = <&e3333_vi_in2>;
									};
								};
							};
						};
						channel@3 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									endpoint@6 {
										status = "okay";
										port-index = <3>;
										bus-width = <2>;
										remote-endpoint = <&e3333_ov5693_out3>;
									};
								};
								port@1 {
									status = "okay";
									endpoint@7 {
										status = "okay";
										remote-endpoint = <&e3333_vi_in3>;
									};
								};
							};
						};
						channel@4 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									endpoint@8 {
										status = "okay";
										port-index = <4>;
										bus-width = <2>;
										remote-endpoint = <&e3333_ov5693_out4>;
									};
								};
								port@1 {
									status = "okay";
									endpoint@9 {
										status = "okay";
										remote-endpoint = <&e3333_vi_in4>;
									};
								};
							};
						};
						channel@5 {
							status = "okay";
							ports {
								status = "okay";
								port@0 {
									status = "okay";
									endpoint@10 {
										status = "okay";
										port-index = <6>;
										bus-width = <2>;
										remote-endpoint = <&e3333_ov5693_out5>;
									};
								};
								port@1 {
									status = "okay";
									endpoint@11 {
										status = "okay";
										remote-endpoint = <&e3333_vi_in5>;
									};
								};
							};
						};
					};
				};

				gpio@2200000 {
					camera-control-output-low {
						gpio-hog;
						output-low;
						gpios = <CAM0_RST_L 0 CAM0_PWDN 0
							CAM1_RST_L 0 CAM1_PWDN 0>;
						label = "cam0-rst", "cam0-pwdn",
							"cam1-rst", "cam1-pwdn";
					};
				};

				i2c@3180000 {
					tca6408_21: tca6408@21 {
						status = "okay";
						compatible = "ti,tca6408";
						gpio-controller;
						#gpio-cells = <2>;
						reg = <0x21>;
						tca6408_21_outlow {
							gpio-hog;
							gpios = <0 0 1 0 2 0 3 0 4 0 5 0 6 0 7 0>;
							output-low;
							label = "tca6408_21_outlow_0",
								"tca6408_21_outlow_1",
								"tca6408_21_outlow_2",
								"tca6408_21_outlow_3",
								"tca6408_21_outlow_4",
								"tca6408_21_outlow_5",
								"tca6408_21_outlow_6",
								"tca6408_21_outlow_7";
							status = "okay";
						};
					};

					tca9548@77 {
						status = "okay";
						compatible = "nxp,pca9548";
						reg = <0x77>;
						#address-cells = <1>;
						#size-cells = <0>;
						skip_mux_detect;
						i2c@0 {
							reg = <0>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ov5693_a@36 {
								status = "okay";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
										<&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								clock-frequency = <24000000>;
								reset-gpios = <&gpio CAM0_RST_L GPIO_ACTIVE_HIGH>;
								pwdn-gpios = <&gpio CAM0_PWDN GPIO_ACTIVE_HIGH>;
								ports {
									status = "okay";
									port@0 {
										status = "okay";
										endpoint {
											status = "okay";
											port-index = <0>;
											bus-width = <2>;
											remote-endpoint = <&e3333_csi_in0>;
										};
									};
								};
							};
						};
						i2c@1 {
							reg = <1>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ov5693_b@36 {
								status = "okay";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
										<&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								clock-frequency = <24000000>;
								reset-gpios = <&gpio CAM1_RST_L GPIO_ACTIVE_HIGH>;
								pwdn-gpios = <&gpio CAM1_PWDN GPIO_ACTIVE_HIGH>;
								ports {
									status = "okay";
									port@0 {
										status = "okay";
										endpoint {
											status = "okay";
											port-index = <1>;
											bus-width = <2>;
											remote-endpoint = <&e3333_csi_in1>;
										};
									};
								};
							};
						};
						i2c@2 {
							reg = <2>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ov5693_c@36 {
								status = "okay";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
										<&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph1", "pllp_grtba";
								mclk = "extperiph1";
								clock-frequency = <24000000>;
								pwdn-gpios = <&tca6408_21 0 GPIO_ACTIVE_HIGH>;
								reset-gpios = <&tca6408_21 1 GPIO_ACTIVE_HIGH>;
								ports {
									status = "okay";
									port@0 {
										status = "okay";
										endpoint {
											status = "okay";
											port-index = <2>;
											bus-width = <2>;
											remote-endpoint = <&e3333_csi_in2>;
										};
									};
								};
							};
						};
						i2c@3 {
							reg = <3>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ov5693_d@36 {
								status = "okay";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH2>,
										<&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph2", "pllp_grtba";
								mclk = "extperiph2";
								clock-frequency = <24000000>;
								pwdn-gpios = <&tca6408_21 2 GPIO_ACTIVE_HIGH>;
								reset-gpios = <&tca6408_21 3 GPIO_ACTIVE_HIGH>;
								ports {
									status = "okay";
									port@0 {
										status = "okay";
										endpoint {
											status = "okay";
											port-index = <3>;
											bus-width = <2>;
											remote-endpoint = <&e3333_csi_in3>;
										};
									};
								};
							};
						};
						i2c@4 {
							reg = <4>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ov5693_e@36 {
								status = "okay";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH2>,
										<&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph2", "pllp_grtba";
								mclk = "extperiph2";
								clock-frequency = <24000000>;
								pwdn-gpios = <&tca6408_21 4 GPIO_ACTIVE_HIGH>;
								reset-gpios = <&tca6408_21 5 GPIO_ACTIVE_HIGH>;
								ports {
									status = "okay";
									port@0 {
										status = "okay";
										endpoint {
											status = "okay";
											port-index = <4>;
											bus-width = <2>;
											remote-endpoint = <&e3333_csi_in4>;
										};
									};
								};
							};
						};
						i2c@5 {
							reg = <5>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;
							ov5693_g@36 {
								status = "okay";
								clocks = <&bpmp TEGRA234_CLK_EXTPERIPH2>,
										<&bpmp TEGRA234_CLK_PLLP_OUT0>;
								clock-names = "extperiph2", "pllp_grtba";
								mclk = "extperiph2";
								clock-frequency = <24000000>;
								pwdn-gpios = <&tca6408_21 6 GPIO_ACTIVE_HIGH>;
								reset-gpios = <&tca6408_21 7 GPIO_ACTIVE_HIGH>;
								ports {
									status = "okay";
									port@0 {
										status = "okay";
										endpoint {
											status = "okay";
											port-index = <5>;
											bus-width = <2>;
											remote-endpoint = <&e3333_csi_in5>;
										};
									};
								};
							};
						};
					};
				};
			};
		};
	};
};
