// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2023-2024, NVIDIA CORPORATION & AFFILIATES. All rights reserved.

/dts-v1/;
/plugin/;

#include "tegra234-p3737-camera-p3762-a00.dtsi"
#include "dt-bindings/gpio/tegra234-gpio.h"
#include "dt-bindings/clock/tegra234-clock.h"
#include <dt-bindings/tegra234-p3737-0000+p3701-0000.h>

#define CAM0_RST_L      TEGRA234_MAIN_GPIO(H, 3)
#define CAM0_PWDN       TEGRA234_MAIN_GPIO(H, 6)
#define CAM1_RST_L      TEGRA234_MAIN_GPIO(AC, 1)
#define CAM1_PWDN       TEGRA234_MAIN_GPIO(AC, 0)
#define PWR_EN      TEGRA234_MAIN_GPIO(AC, 7)
#define GYRO1_IRQ_GPIO  TEGRA234_AON_GPIO(CC, 1)
#define ACCE1_IRQ_GPIO  TEGRA234_AON_GPIO(CC, 0)

/* camera control gpio definitions */
/ {
	overlay-name = "Jetson Camera Hawk-Owl p3762 module";
	jetson-header-name = "Jetson AGX CSI Connector";
	compatible = JETSON_COMPATIBLE;

	fragment-camera-hawk-owl@0 {
		target-path = "/";
		__overlay__ {
			bus@0 {
				i2c@3180000 {
					max96712_b@62 {
						status = "okay";
					};
					ar0234_i@30 {
						status = "okay";
					};
					ar0234_j@32 {
						status = "okay";
					};
					ar0234_k@34 {
						status = "okay";
					};
					ar0234_l@36 {
						status = "okay";
					};
				};
				i2c@31e0000 {
					max96712_a@62 {
						status = "okay";
					};
					virtual_i2c_mux@50 {
						status = "okay";
						i2c@0 {
							bmi088_a@69 {
								status = "okay";
							};
							ar0234_a@30 {
								status = "okay";
							};
							ar0234_b@31 {
								status = "okay";
							};
						};
						i2c@1 {
							ar0234_c@32 {
								status = "okay";
							};
							ar0234_d@33 {
								status = "okay";
							};
							ar0234_e@34 {
								status = "okay";
							};
							ar0234_f@35 {
								status = "okay";
							};
							ar0234_g@36 {
								status = "okay";
							};
							ar0234_h@37 {
								status = "okay";
							};
						};
					};
				};
			};
		};
	};
};
