// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2023, NVIDIA CORPORATION & AFFILIATES. All rights reserved.

/dts-v1/;
/plugin/;

#define CAM0_RST    	TEGRA234_MAIN_GPIO(H, 3)
#define CAM0_PWDN		TEGRA234_MAIN_GPIO(H, 6)
#define CAM1_PWDN		TEGRA234_MAIN_GPIO(AC, 0)

#define CAM_I2C_MUX 	TEGRA234_AON_GPIO(CC, 3)

#define CAM_LANES 4
#define CAM_LANES_STRING "4"

#include <dt-bindings/tegra234-p3767-0000-common.h>

/ {
	overlay-name = "TechNexion Camera TEVS Dual";
	jetson-header-name = "Jetson 24pin CSI Connector";

	fragment-camera-tevs@0 {
		target-path = "/";
		__overlay__ {
			tegra-capture-vi  {
				num-channels = <2>;

				ports {
					#address-cells = <1>;
					#size-cells = <0>;

					port@0 {
						reg = <0>;

						rpi22_tevs_vi_in0: endpoint {
							port-index = <0>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&rpi22_tevs_csi_out0>;
						};
					};

					port@1 {
						reg = <1>;

						rpi22_tevs_vi_in1: endpoint {
							port-index = <2>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&rpi22_tevs_csi_out1>;
						};
					};
				};
			};

			tcp: tegra-camera-platform {
				compatible = "nvidia, tegra-camera-platform";
				/**
				* Physical settings to calculate max ISO BW
				*
				* num_csi_lanes = <>;
				* Total number of CSI lanes when all cameras are active
				*
				* max_lane_speed = <>;
				* Max lane speed in Kbit/s
				*
				* min_bits_per_pixel = <>;
				* Min bits per pixel
				*
				* vi_peak_byte_per_pixel = <>;
				* Max byte per pixel for the VI ISO case
				*
				* vi_bw_margin_pct = <>;
				* Vi bandwidth margin in percentage
				*
				* max_pixel_rate = <>;
				* Max pixel rate in Kpixel/s for the ISP ISO case
				*
				* isp_peak_byte_per_pixel = <>;
				* Max byte per pixel for the ISP ISO case
				*
				* isp_bw_margin_pct = <>;
				* Isp bandwidth margin in percentage
				*/
				num_csi_lanes = <8>;
				max_lane_speed = <1500000>;
				min_bits_per_pixel = <10>;
				vi_peak_byte_per_pixel = <2>;
				vi_bw_margin_pct = <25>;
				max_pixel_rate = <240000>;
				isp_peak_byte_per_pixel = <5>;
				isp_bw_margin_pct = <25>;
				/**
				 * The general guideline for naming badge_info contains 3 parts, and is as follows,
				 * The first part is the camera_board_id for the module; if the module is in a FFD
				 * platform, then use the platform name for this part.
				 * The second part contains the position of the module, ex. "rear" or "front".
				 * The third part contains the last 6 characters of a part number which is found
				 * in the module's specsheet from the vendor.
				 */
				modules {
					module0 {
						badge = "jakku_front_RBP194";
						position = "front";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/cam_i2cmux/i2c@0/rpi22_tevs_a@48";
						};
					};
					module1 {
						badge = "jakku_rear_RBP194";
						position = "rear";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/cam_i2cmux/i2c@1/rpi22_tevs_c@48";
						};
					};
				};
			};

			bus@0 {
				host1x@13e00000 {
					nvcsi@15a00000 {
						num-channels = <2>;
						#address-cells = <1>;
						#size-cells = <0>;
						channel@0 {
							reg = <0>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;

								port@0 {
									reg = <0>;
									rpi22_tevs_csi_in0: endpoint@0 {
										port-index = <0>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_out0>;
									};
								};

								port@1 {
									reg = <1>;
									rpi22_tevs_csi_out0: endpoint@1 {
										remote-endpoint = <&rpi22_tevs_vi_in0>;
									};
								};
							};
						};

						channel@1 {
							reg = <1>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;

								port@0 {
									reg = <0>;
									rpi22_tevs_csi_in1: endpoint@2 {
										port-index = <2>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_out1>;
									};
								};

								port@1 {
									reg = <1>;
									rpi22_tevs_csi_out1: endpoint@3 {
										remote-endpoint = <&rpi22_tevs_vi_in1>;
									};
								};
							};
						};
					};
				};

				gpio@2200000 {
					camera-control-output-low {
						gpio-hog;
						output-low;
						gpios = <CAM0_RST 0>;
						label = "cam0-rst";
					};
				};

				cam_i2cmux{
					status = "okay";
					compatible = "i2c-mux-gpio";
					#address-cells = <1>;
					#size-cells = <0>;
					i2c-parent = <&cam_i2c>;
					mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

					i2c@1 {
						status = "okay";
						reg = <1>;
						#address-cells = <1>;
						#size-cells = <0>;

						rbpcv2_imx219_c@10 {
							status = "disabled";
						};

						pca9554_a27_c: pca9554_c@27 {
							compatible = "nxp,pca9554";
							reg = <0x27>;
							gpio-controller;
							#gpio-cells = <2>;
							vcc-supply = <&vdd_1v8_sys>;
							vcc_lp = "vcc";
							status = "okay";
						};

						tevs_cam1: rpi22_tevs_c@48 {
							status = "okay";
							compatible = "tn,tevs";
							/* I2C device address */
							reg = <0x48>;

							reset-gpios = <&gpio CAM1_PWDN GPIO_ACTIVE_HIGH>;
							standby-gpios = <&pca9554_a27_c 6 GPIO_ACTIVE_HIGH>;
							data-lanes = <CAM_LANES>;
							data-frequency = <800>;

							/* V4L2 device node location */
							devnode = "video1";
							/* Physical dimensions of sensor */
							physical_w = "3.680";
							physical_h = "2.760";
							sensor_model = "tevs";
							use_sensor_mode_id = "false";
							/* Modes */
							mode0 { /* TEVS_MODE_3280x2464_21FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_c";
								lane_polarity = "0";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "3280";
								active_h = "2464";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "21000000"; /* 21.0 fps */
								step_framerate = "1";
								default_framerate = "21000000"; /* 21.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode1 { /* TEVS_MODE_3280x1848_28FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_c";
								lane_polarity = "0";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "3280";
								active_h = "1848";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "28000000"; /* 28.0 fps */
								step_framerate = "1";
								default_framerate = "28000000"; /* 28.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode2 { /* TEVS_MODE_1920x1080_30FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_c";
								lane_polarity = "0";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "1920";
								active_h = "1080";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "30000000"; /* 30.0 fps */
								step_framerate = "1";
								default_framerate = "30000000"; /* 30.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode3 { /* TEVS_MODE_1640x1232_30FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_c";
								lane_polarity = "0";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "1640";
								active_h = "1232";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "30000000"; /* 60.0 fps */
								step_framerate = "1";
								default_framerate = "30000000"; /* 60.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode4 { /* TEVS_MODE_1280x720_60FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_c";
								lane_polarity = "0";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "1280";
								active_h = "720";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "60000000"; /* 60.0 fps */
								step_framerate = "1";
								default_framerate = "60000000"; /* 60.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode5 { /* TEVS_MODE_640x480_60FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_c";
								lane_polarity = "0";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "640";
								active_h = "480";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "60000000"; /* 60.0 fps */
								step_framerate = "1";
								default_framerate = "60000000"; /* 60.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									rpi22_tevs_out1: endpoint {
										port-index = <2>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_csi_in1>;
									};
								};
							};
						};
					};

					i2c@0 {
						status = "okay";
						reg = <0>;
						#address-cells = <1>;
						#size-cells = <0>;

						rbpcv2_imx219_a@10 {
							status = "disabled";
						};

						pca9554_a27_a: pca9554_a@27 {
							compatible = "nxp,pca9554";
							reg = <0x27>;
							gpio-controller;
							#gpio-cells = <2>;
							vcc-supply = <&vdd_1v8_hs>;
							vcc_lp = "vcc";
							status = "okay";
						};

						tevs_cam0: rpi22_tevs_a@48 {
							status = "okay";
							compatible = "tn,tevs";
							/* I2C device address */
							reg = <0x48>;

							reset-gpios = <&gpio CAM0_PWDN GPIO_ACTIVE_HIGH>;
							standby-gpios = <&pca9554_a27_a 6 GPIO_ACTIVE_HIGH>;
							data-lanes = <CAM_LANES>;
							data-frequency = <800>;

							/* V4L2 device node location */
							devnode = "video0";
							/* Physical dimensions of sensor */
							physical_w = "3.680";
							physical_h = "2.760";
							sensor_model = "tevs";
							use_sensor_mode_id = "false";
							/* Modes */
							mode0 { /* TEVS_MODE_3280x2464_21FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_b";
								lane_polarity = "6";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "3280";
								active_h = "2464";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "21000000"; /* 21.0 fps */
								step_framerate = "1";
								default_framerate = "21000000"; /* 21.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode1 { /* TEVS_MODE_3280x1848_28FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_b";
								lane_polarity = "6";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "3280";
								active_h = "1848";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "28000000"; /* 28.0 fps */
								step_framerate = "1";
								default_framerate = "28000000"; /* 28.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode2 { /* TEVS_MODE_1920x1080_30FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_b";
								lane_polarity = "6";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "1920";
								active_h = "1080";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "30000000"; /* 30.0 fps */
								step_framerate = "1";
								default_framerate = "30000000"; /* 30.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode3 { /* TEVS_MODE_1640x1232_30FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_b";
								lane_polarity = "6";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "1640";
								active_h = "1232";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "30000000"; /* 60.0 fps */
								step_framerate = "1";
								default_framerate = "30000000"; /* 60.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode4 { /* TEVS_MODE_1280x720_60FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_b";
								lane_polarity = "6";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "1280";
								active_h = "720";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "60000000"; /* 60.0 fps */
								step_framerate = "1";
								default_framerate = "60000000"; /* 60.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							mode5 { /* TEVS_MODE_640x480_60FPS */
								mclk_khz = "24000";
								num_lanes = CAM_LANES_STRING;
								tegra_sinterface = "serial_b";
								lane_polarity = "6";
								phy_mode = "DPHY";
								discontinuous_clk = "yes";
								dpcm_enable = "false";
								cil_settletime = "0";

								active_w = "640";
								active_h = "480";
								mode_type = "yuv";
								pixel_phase = "uyvy";
								csi_pixel_bit_depth = "16";
								readout_orientation = "90";
								line_length = "3448";
								inherent_gain = "1";
								mclk_multiplier = "9.33";
								pix_clk_hz = "200000000";

								gain_factor = "16";
								framerate_factor = "1000000";
								exposure_factor = "1000000";
								min_gain_val = "16"; /* 1.00x */
								max_gain_val = "170"; /* 10.66x */
								step_gain_val = "1";
								default_gain = "16"; /* 1.00x */
								min_hdr_ratio = "1";
								max_hdr_ratio = "1";
								min_framerate = "2000000"; /* 2.0 fps */
								max_framerate = "60000000"; /* 60.0 fps */
								step_framerate = "1";
								default_framerate = "60000000"; /* 60.0 fps */
								min_exp_time = "13"; /* us */
								max_exp_time = "683709"; /* us */
								step_exp_time = "1";
								default_exp_time = "2495"; /* us */
								embedded_metadata_height = "0";
							};
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								port@0 {
									reg = <0>;
									rpi22_tevs_out0: endpoint {
										port-index = <0>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_csi_in0>;
									};
								};
							};
						};
					};
				};
			};
		};
	};
};
