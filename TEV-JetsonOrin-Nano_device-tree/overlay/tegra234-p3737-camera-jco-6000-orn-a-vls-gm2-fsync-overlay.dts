// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2015-2024, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

/dts-v1/;
/plugin/;

#include <dt-bindings/clock/tegra234-clock.h>
#include <dt-bindings/gpio/tegra234-gpio.h>
#include <dt-bindings/tegra234-p3737-0000+p3701-0000.h>

#define CAM0_RST_L		TEGRA234_MAIN_GPIO(H, 3)
#define CAM0_PWDN		TEGRA234_MAIN_GPIO(H, 6)
#define CAM1_RST_L		TEGRA234_MAIN_GPIO(AC, 1)
#define CAM1_PWDN		TEGRA234_MAIN_GPIO(AC, 0)

#define CAM_FRSYNC1		TEGRA234_MAIN_GPIO(BB, 2)
#define CAM_FRSYNC2		TEGRA234_MAIN_GPIO(AA, 7)
#define CAM_FRSYNC3		TEGRA234_MAIN_GPIO(AA, 4)
#define CAM_FRSYNC4		TEGRA234_MAIN_GPIO(AC, 2)

#define CAM_I2C_MUX 	TEGRA234_AON_GPIO(CC, 3)

#define CAM_LANES 4
#define CAM_LANES_STRING "4"
#define GMSL_ARCAM_NUM 8

/ {
	overlay-name = "TechNexion VLS-GM2 Camera for frame sync";
	jetson-header-name = "Jetson AGX CSI Connector";
	compatible = JETSON_COMPATIBLE;

	fragment@0 {
		target-path = "/";
		__overlay__ {
			tegra-capture-vi  {
				num-channels = <GMSL_ARCAM_NUM>;
				ports {
					#address-cells = <1>;
					#size-cells = <0>;

					vi_port0: port@0 {
						status = "okay";
						reg = <0>;

						vi_in0: endpoint {
							status = "okay";
							vc-id = <0>;
							port-index = <0>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&csi_0_out>;
						};
					};

					vi_port1: port@1 {
						status = "okay";
						reg = <1>;

						vi_in1: endpoint {
							status = "okay";
							vc-id = <1>;
							port-index = <0>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&csi_1_out>;
						};
					};

					vi_port2: port@2 {
						status = "okay";
						reg = <2>;

						vi_in2: endpoint {
							status = "okay";
							vc-id = <0>;
							port-index = <2>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&csi_2_out>;
						};
					};

					vi_port3: port@3 {
						status = "okay";
						reg = <3>;

						vi_in3: endpoint {
							status = "okay";
							vc-id = <1>;
							port-index = <2>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&csi_3_out>;
						};
					};

					vi_port4: port@4 {
						status = "okay";
						reg = <4>;

						vi_in4: endpoint {
							status = "okay";
							vc-id = <0>;
							port-index = <4>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&csi_4_out>;
						};
					};

					vi_port5: port@5 {
						status = "okay";
						reg = <5>;

						vi_in5: endpoint {
							status = "okay";
							vc-id = <1>;
							port-index = <4>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&csi_5_out>;
						};
					};

					vi_port6: port@6 {
						status = "okay";
						reg = <6>;

						vi_in6: endpoint {
							status = "okay";
							vc-id = <0>;
							port-index = <5>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&csi_6_out>;
						};
					};

					vi_port7: port@7 {
						status = "okay";
						reg = <7>;

						vi_in7: endpoint {
							status = "okay";
							vc-id = <1>;
							port-index = <5>;
							bus-width = <CAM_LANES>;
							remote-endpoint = <&csi_7_out>;
						};
					};
				};
			};

			tegra-camera-platform {
				compatible = "nvidia, tegra-camera-platform";

				num_csi_lanes = <16>;
				max_lane_speed = <2500000>;
				min_bits_per_pixel = <16>;
				vi_peak_byte_per_pixel = <2>;
				vi_bw_margin_pct = <67>;
				// max_pixel_rate = <240000>;
				// isp_peak_byte_per_pixel = <5>;
				// isp_bw_margin_pct = <25>;

				modules {
					module0 {
						badge = "tevs_rear";
						position = "rear";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@0/gmsl-deserializer@4a/i2c@0/gmsl-serializer@41/i2c@0/rpi22_tevs_a@3d";
						};
					};

					module1 {
						badge = "tevs_front";
						position = "front";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@0/gmsl-deserializer@4a/i2c@1/gmsl-serializer@42/i2c@0/rpi22_tevs_b@3e";
						};
					};

					module2 {
						badge = "tevs_left";
						position = "left";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@1/gmsl-deserializer@4a/i2c@0/gmsl-serializer@41/i2c@0/rpi22_tevs_c@3d";
						};
					};

					module3 {
						badge = "tevs_right";
						position = "right";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@1/gmsl-deserializer@4a/i2c@1/gmsl-serializer@42/i2c@0/rpi22_tevs_d@3e";
						};
					};

					module4 {
						badge = "tevs_rear4";
						position = "rear4";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@2/gmsl-deserializer@68/i2c@0/gmsl-serializer@41/i2c@0/rpi22_tevs_e@3d";
						};
					};

					module5 {
						badge = "tevs_front4";
						position = "front4";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@2/gmsl-deserializer@68/i2c@1/gmsl-serializer@42/i2c@0/rpi22_tevs_f@3e";
						};
					};

					module6 {
						badge = "tevs_left4";
						position = "left4";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@3/gmsl-deserializer@68/i2c@0/gmsl-serializer@41/i2c@0/rpi22_tevs_g@3d";
						};
					};

					module7 {
						badge = "tevs_right4";
						position = "right4";
						orientation = "1";
						drivernode0 {
							pcl_id = "v4l2_sensor";
							sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/tca9546@70/i2c@3/gmsl-deserializer@68/i2c@1/gmsl-serializer@42/i2c@0/rpi22_tevs_h@3e";
						};
					};
				};
			};

			bus@0 {
				host1x@13e00000 {
					nvcsi@15a00000 {
						#address-cells = <1>;
						#size-cells = <0>;

						num-channels = <GMSL_ARCAM_NUM>;

						channel@0 {
							status = "okay";
							reg = <0>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								status = "okay";

								port@0 {
									status = "okay";
									reg = <0>;
									csi_0_in: endpoint@0 {
										status = "okay";
										port-index = <0>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_0_out>;
									};
								};

								port@1 {
									status = "okay";
									reg = <1>;
									csi_0_out: endpoint@1 {
										status = "okay";
										remote-endpoint = <&vi_in0>;
									};
								};
							};
						};

						channel@1 {
							status = "okay";
							reg = <1>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								status = "okay";

								port@0 {
									status = "okay";
									reg = <0>;
									csi_1_in: endpoint@2 {
										status = "okay";
										port-index = <0>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_1_out>;
									};
								};

								port@1 {
									status = "okay";
									reg = <1>;
									csi_1_out: endpoint@3 {
										status = "okay";
										remote-endpoint = <&vi_in1>;
									};
								};
							};
						};

						channel@2 {
							status = "okay";
							reg = <2>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								status = "okay";

								port@0 {
									status = "okay";
									reg = <0>;
									csi_2_in: endpoint@4 {
										status = "okay";
										port-index = <2>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_2_out>;
									};
								};

								port@1 {
									status = "okay";
									reg = <1>;
									csi_2_out: endpoint@5 {
										status = "okay";
										remote-endpoint = <&vi_in2>;
									};
								};
							};
						};

						channel@3 {
							status = "okay";
							reg = <3>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								status = "okay";

								port@0 {
									status = "okay";
									reg = <0>;
									csi_3_in: endpoint@6 {
										status = "okay";
										port-index = <2>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_3_out>;
									};
								};

								port@1 {
									status = "okay";
									reg = <1>;
									csi_3_out: endpoint@7 {
										status = "okay";
										remote-endpoint = <&vi_in3>;
									};
								};
							};
						};

						channel@4 {
							status = "okay";
							reg = <4>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								status = "okay";

								port@0 {
									status = "okay";
									reg = <0>;
									csi_4_in: endpoint@8 {
										status = "okay";
										port-index = <4>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_4_out>;
									};
								};

								port@1 {
									status = "okay";
									reg = <1>;
									csi_4_out: endpoint@9 {
										status = "okay";
										remote-endpoint = <&vi_in4>;
									};
								};
							};
						};

						channel@5 {
							status = "okay";
							reg = <5>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								status = "okay";

								port@0 {
									status = "okay";
									reg = <0>;
									csi_5_in: endpoint@10 {
									status = "okay";
										port-index = <4>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_5_out>;
									};
								};

								port@1 {
									status = "okay";
									reg = <1>;
									csi_5_out: endpoint@11 {
										status = "okay";
										remote-endpoint = <&vi_in5>;
									};
								};
							};
						};

						channel@6 {
							status = "okay";
							reg = <6>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								status = "okay";

								port@0 {
									status = "okay";
									reg = <0>;
									csi_6_in: endpoint@12 {
										status = "okay";
										port-index = <6>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_6_out>;
									};
								};

								port@1 {
									status = "okay";
									reg = <1>;
									csi_6_out: endpoint@13 {
										status = "okay";
										remote-endpoint = <&vi_in6>;
									};
								};
							};
						};

						channel@7 {
							status = "okay";
							reg = <7>;
							ports {
								#address-cells = <1>;
								#size-cells = <0>;
								status = "okay";

								port@0 {
									status = "okay";
									reg = <0>;
									csi_7_in: endpoint@14 {
										status = "okay";
										port-index = <6>;
										bus-width = <CAM_LANES>;
										remote-endpoint = <&rpi22_tevs_7_out>;
									};
								};

								port@1 {
									status = "okay";
									reg = <1>;
									csi_7_out: endpoint@15 {
										status = "okay";
										remote-endpoint = <&vi_in7>;
									};
								};
							};
						};
					};
				};

				gpio@2200000 {
					camera-control-output-low {
						status = "disabled";
						gpio-hog;
						output-low;
						gpios = <CAM0_RST_L 0 CAM0_PWDN 0
							CAM1_RST_L 0 CAM1_PWDN 0>;
						label = "cam0-rst", "cam0-pwdn",
							"cam1-rst", "cam1-pwdn";
					};
				};

				i2c@3180000 {
					tca9546@70 {
						#address-cells = <1>;
						#size-cells = <0>;

						status = "okay";
						compatible = "nxp,pca9546";
						reg = <0x70>;
						skip_mux_detect = "yes";

						cam_i2c_0: i2c@0 {
							reg = <0>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;

							imx390@10 {
								status = "disabled";
							};
							imx390@11 {
								status = "disabled";
							};
							max9295_a@40 {
								status = "disabled";
							};
							max9295_b@60 {
								status = "disabled";
							};
							max9295_prim@62 {
								status = "disabled";
							};
							max9296_a@48 {
								status = "disabled";
							};
						};
						cam_i2c_1: i2c@1 {
							reg = <1>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;

							imx390@12 {
								status = "disabled";
							};
							imx390@13 {
								status = "disabled";
							};
							max9295_c@40 {
								status = "disabled";
							};
							max9295_d@60 {
								status = "disabled";
							};
							max9295_prim@62 {
								status = "disabled";
							};
							max9296_c@48 {
								status = "disabled";
							};
						};
						cam_i2c_2: i2c@2 {
							reg = <2>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;

							imx390@14 {
								status = "disabled";
							};
							imx390@15 {
								status = "disabled";
							};
							max9295_e@40 {
								status = "disabled";
							};
							max9295_f@60 {
								status = "disabled";
							};
							max9295_prim@62 {
								status = "disabled";
							};
							max9296_e@48 {
								status = "disabled";
							};
						};
						cam_i2c_3: i2c@3 {
							reg = <3>;
							i2c-mux,deselect-on-exit;
							#address-cells = <1>;
							#size-cells = <0>;

							imx390@16 {
								status = "disabled";
							};
							imx390@17 {
								status = "disabled";
							};
							max9295_g@40 {
								status = "disabled";
							};
							max9295_h@60 {
								status = "disabled";
							};
							max9295_prim@62 {
								status = "disabled";
							};
							max9296_g@48 {
								status = "disabled";
							};
						};
					};
				};
			};
		};
	};
};

&cam_i2c_0 {
	des_0: gmsl-deserializer@4a {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max9296a";
		reg = <0x4a>;
		phy-reg = <0x48>;

		reset-gpios = <&gpio CAM0_PWDN GPIO_ACTIVE_LOW>;

		//i2c addr alias map "serializer0, serializer1"
		i2c-addr-alias-map-local = <0x41 0x42>;
		i2c-addr-alias-map-remote = <0x40 0x40>;
		i2c-addr-alias-source-id = <0 1>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&des_0 0 0 12>;

		pinctrl-names = "default";
		pinctrl-0 = <&des_0_pinmux_default>;

		fsync-mode = "external";

		des_0_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-tx = <1>;
				maxim,gmsl-tx-id = <0>;
				input-enable;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			des_0_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};

			des_0_i2c_1: i2c@1 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <1>;
			};
		};

		phy@0 {
			reg = <0>;
		};

		pipe@0 {
			reg = <0>;

			maxim,phy-id = <0>;
		};

		pipe@1 {
			reg = <1>;

			maxim,phy-id = <0>;
		};

		channel@0 {
			reg = <0>;
			label = "des_0_ch_0";

			maxim,phy-id = <0>;

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			link-frequencies = /bits/ 64 <1000000000>;
		};

		channel@1 {
			reg = <1>;
			label = "des_0_ch_1";

			maxim,phy-id = <0>;

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			link-frequencies = /bits/ 64 <1000000000>;
		};
	};
};

&cam_i2c_1 {
	des_1: gmsl-deserializer@4a {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max9296a";
		reg = <0x4a>;
		phy-reg = <0x48>;

		reset-gpios = <&gpio CAM0_RST_L GPIO_ACTIVE_LOW>;

		//i2c addr alias map "serializer0, serializer1"
		i2c-addr-alias-map-local = <0x41 0x42>;
		i2c-addr-alias-map-remote = <0x40 0x40>;
		i2c-addr-alias-source-id = <0 1>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&des_1 0 0 12>;

		pinctrl-names = "default";
		pinctrl-0 = <&des_1_pinmux_default>;

		fsync-mode = "external";

		des_1_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-tx = <1>;
				maxim,gmsl-tx-id = <0>;
				input-enable;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			des_1_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};

			des_1_i2c_1: i2c@1 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <1>;
			};
		};

		phy@0 {
			reg = <0>;
		};

		pipe@0 {
			reg = <0>;

			maxim,phy-id = <0>;
		};

		pipe@1 {
			reg = <1>;

			maxim,phy-id = <0>;
		};

		channel@0 {
			reg = <0>;
			label = "des_1_ch_0";

			maxim,phy-id = <0>;

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			link-frequencies = /bits/ 64 <1000000000>;
		};

		channel@1 {
			reg = <1>;
			label = "des_1_ch_1";

			maxim,phy-id = <0>;

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			link-frequencies = /bits/ 64 <1000000000>;
		};
	};
};

&cam_i2c_2 {
	des_2: gmsl-deserializer@68 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max9296a";
		reg = <0x68>;
		phy-reg = <0x48>;

		reset-gpios = <&gpio CAM1_PWDN GPIO_ACTIVE_LOW>;

		//i2c addr alias map "serializer0, serializer1"
		i2c-addr-alias-map-local = <0x41 0x42>;
		i2c-addr-alias-map-remote = <0x40 0x40>;
		i2c-addr-alias-source-id = <0 1>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&des_2 0 0 12>;

		pinctrl-names = "default";
		pinctrl-0 = <&des_2_pinmux_default>;

		fsync-mode = "external";

		des_2_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-tx = <1>;
				maxim,gmsl-tx-id = <0>;
				input-enable;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			des_2_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};

			des_2_i2c_1: i2c@1 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <1>;
			};
		};

		phy@0 {
			reg = <0>;
		};

		pipe@0 {
			reg = <0>;

			maxim,phy-id = <0>;
		};

		pipe@1 {
			reg = <1>;

			maxim,phy-id = <0>;
		};

		channel@0 {
			reg = <0>;
			label = "des_2_ch_0";

			maxim,phy-id = <0>;

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			link-frequencies = /bits/ 64 <1000000000>;
		};

		channel@1 {
			reg = <1>;
			label = "des_2_ch_1";

			maxim,phy-id = <0>;

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			link-frequencies = /bits/ 64 <1000000000>;
		};
	};
};

&cam_i2c_3 {
	des_3: gmsl-deserializer@68 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max9296a";
		reg = <0x68>;
		phy-reg = <0x48>;

		reset-gpios = <&gpio CAM1_RST_L GPIO_ACTIVE_LOW>;

		//i2c addr alias map "serializer0, serializer1"
		i2c-addr-alias-map-local = <0x41 0x42>;
		i2c-addr-alias-map-remote = <0x40 0x40>;
		i2c-addr-alias-source-id = <0 1>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&des_3 0 0 12>;

		pinctrl-names = "default";
		pinctrl-0 = <&des_3_pinmux_default>;

		fsync-mode = "external";

		des_3_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-tx = <1>;
				maxim,gmsl-tx-id = <0>;
				input-enable;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			des_3_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};

			des_3_i2c_1: i2c@1 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <1>;
			};
		};

		phy@0 {
			reg = <0>;
		};

		pipe@0 {
			reg = <0>;

			maxim,phy-id = <0>;
		};

		pipe@1 {
			reg = <1>;

			maxim,phy-id = <0>;
		};

		channel@0 {
			reg = <0>;
			label = "des_3_ch_0";

			maxim,phy-id = <0>;

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			link-frequencies = /bits/ 64 <1000000000>;
		};

		channel@1 {
			reg = <1>;
			label = "des_3_ch_1";

			maxim,phy-id = <0>;

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			link-frequencies = /bits/ 64 <1000000000>;
		};
	};
};

&des_0_i2c_0 {
	ser_0: gmsl-serializer@41 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max96717";
		reg = <0x41>;

		// i2c addr alias map "sensor, gpio expander"
		i2c-addr-alias-map-local = <0x3d 0x26>;
		i2c-addr-alias-map-remote = <0x48 0x25>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&ser_0 0 0 11>;

		pinctrl-names = "default";
		pinctrl-0 = <&ser_0_pinmux_default>;

		ser_0_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-rx = <1>;
				maxim,gmsl-rx-id = <0>;
				output-enable;
				output-low;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			ser_0_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};
		};

		pipe@0 {
			reg = <0>;

			maxim,stream-id = <0>;
		};

		channel@0 {
			reg = <0>;
			label = "ser_0_ch_0";

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			clock-noncontinuous;
		};
	};
};

&ser_0_i2c_0 {
	pca9554_cam_0: pca9554@26 {
		compatible = "nxp,pca9554";
		reg = <0x26>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "EXPOSURE_TRIG_IN",
						"FLASH_OUT",
						"INFO_TRIG_IN",
						"CAMA_SHUTTER",
						"CSI_P1_nRST",
						"PWR_TIME0",
						"PWR_TIME1",
						"3V3_EN";
		vcc-supply = <&vdd_1v8_hs>;
		vcc_lp = "vcc";
		status = "okay";
	};

	cam_0: rpi22_tevs_a@3d {
		compatible = "tn,tevs";
		/* I2C device address */
		reg = <0x3d>;
		status = "okay";

		reset-gpios = <&pca9554_cam_0 4 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_cam_0 2 GPIO_ACTIVE_HIGH>;
		data-lanes = <CAM_LANES>;
		data-frequency = <800>;
		trigger-mode = <2>;
		// hw-reset;

		/* V4L2 device node location */
		devnode = "video0";
		/* Physical dimensions of sensor */
		physical_w = "3.680";
		physical_h = "2.760";
		sensor_model = "tevs";
		use_sensor_mode_id = "false";

		mode0 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "2464";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "21000000"; /* 21.0 fps */
			step_framerate = "1";
			default_framerate = "21000000"; /* 21.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode1 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "1848";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "28000000"; /* 28.0 fps */
			step_framerate = "1";
			default_framerate = "28000000"; /* 28.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode2 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1920";
			active_h = "1080";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 30.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 30.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode3 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1640";
			active_h = "1232";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode4 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1280";
			active_h = "720";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode5 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "640";
			active_h = "480";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;

				rpi22_tevs_0_out: endpoint {
					port-index = <0>;
					bus-width = <CAM_LANES>;
					remote-endpoint = <&csi_0_in>;
				};
			};
		};
	};
};

&des_0_i2c_1 {
	ser_1: gmsl-serializer@42 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max96717";
		reg = <0x42>;

		// i2c addr alias map "sensor, gpio expander"
		i2c-addr-alias-map-local = <0x3e 0x27>;
		i2c-addr-alias-map-remote = <0x48 0x25>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&ser_1 0 0 11>;

		pinctrl-names = "default";
		pinctrl-0 = <&ser_1_pinmux_default>;

		ser_1_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-rx = <1>;
				maxim,gmsl-rx-id = <0>;
				output-enable;
				output-low;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			ser_1_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};
		};

		pipe@0 {
			reg = <0>;

			maxim,stream-id = <1>;
		};

		channel@0 {
			reg = <0>;
			label = "ser_1_ch_0";

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			clock-noncontinuous;
		};
	};
};

&ser_1_i2c_0 {
	pca9554_cam_1: pca9554@27 {
		compatible = "nxp,pca9554";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "EXPOSURE_TRIG_IN",
						"FLASH_OUT",
						"INFO_TRIG_IN",
						"CAMA_SHUTTER",
						"CSI_P1_nRST",
						"PWR_TIME0",
						"PWR_TIME1",
						"3V3_EN";
		vcc-supply = <&vdd_1v8_hs>;
		vcc_lp = "vcc";
		status = "okay";
	};

	cam_1: rpi22_tevs_b@3e {
		compatible = "tn,tevs";
		/* I2C device address */
		reg = <0x3e>;
		status = "okay";

		reset-gpios = <&pca9554_cam_1 4 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_cam_1 2 GPIO_ACTIVE_HIGH>;
		data-lanes = <CAM_LANES>;
		data-frequency = <800>;
		trigger-mode = <2>;
		// hw-reset;

		/* V4L2 device node location */
		devnode = "video1";
		/* Physical dimensions of sensor */
		physical_w = "3.680";
		physical_h = "2.760";
		sensor_model = "tevs";
		use_sensor_mode_id = "false";

		mode0 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "2464";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "21000000"; /* 21.0 fps */
			step_framerate = "1";
			default_framerate = "21000000"; /* 21.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode1 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "1848";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "28000000"; /* 28.0 fps */
			step_framerate = "1";
			default_framerate = "28000000"; /* 28.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode2 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1920";
			active_h = "1080";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 30.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 30.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode3 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1640";
			active_h = "1232";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode4 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1280";
			active_h = "720";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode5 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_a";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "640";
			active_h = "480";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;

				rpi22_tevs_1_out: endpoint {
					port-index = <0>;
					bus-width = <CAM_LANES>;
					remote-endpoint = <&csi_1_in>;
				};
			};
		};
	};
};

&des_1_i2c_0 {
	ser_2: gmsl-serializer@41 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max96717";
		reg = <0x41>;

		// i2c addr alias map "sensor, gpio expander"
		i2c-addr-alias-map-local = <0x3d 0x26>;
		i2c-addr-alias-map-remote = <0x48 0x25>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&ser_2 0 0 11>;

		pinctrl-names = "default";
		pinctrl-0 = <&ser_2_pinmux_default>;

		ser_2_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-rx = <1>;
				maxim,gmsl-rx-id = <0>;
				output-enable;
				output-low;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			ser_2_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};
		};

		pipe@0 {
			reg = <0>;

			maxim,stream-id = <0>;
		};

		channel@0 {
			reg = <0>;
			label = "ser_2_ch_0";

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			clock-noncontinuous;
		};
	};
};

&ser_2_i2c_0 {
	pca9554_cam_2: pca9554@26 {
		compatible = "nxp,pca9554";
		reg = <0x26>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "EXPOSURE_TRIG_IN",
						"FLASH_OUT",
						"INFO_TRIG_IN",
						"CAMA_SHUTTER",
						"CSI_P1_nRST",
						"PWR_TIME0",
						"PWR_TIME1",
						"3V3_EN";
		vcc-supply = <&vdd_1v8_hs>;
		vcc_lp = "vcc";
		status = "okay";
	};

	cam_2: rpi22_tevs_c@3d {
		compatible = "tn,tevs";
		/* I2C device address */
		reg = <0x3d>;
		status = "okay";

		reset-gpios = <&pca9554_cam_2 4 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_cam_2 2 GPIO_ACTIVE_HIGH>;
		data-lanes = <CAM_LANES>;
		data-frequency = <800>;
		trigger-mode = <2>;
		// hw-reset;

		/* V4L2 device node location */
		devnode = "video2";
		/* Physical dimensions of sensor */
		physical_w = "3.680";
		physical_h = "2.760";
		sensor_model = "tevs";
		use_sensor_mode_id = "false";

		mode0 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "2464";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "21000000"; /* 21.0 fps */
			step_framerate = "1";
			default_framerate = "21000000"; /* 21.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode1 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "1848";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "28000000"; /* 28.0 fps */
			step_framerate = "1";
			default_framerate = "28000000"; /* 28.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode2 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1920";
			active_h = "1080";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 30.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 30.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode3 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1640";
			active_h = "1232";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode4 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1280";
			active_h = "720";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode5 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "640";
			active_h = "480";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;

				rpi22_tevs_2_out: endpoint {
					port-index = <2>;
					bus-width = <CAM_LANES>;
					remote-endpoint = <&csi_2_in>;
				};
			};
		};
	};
};

&des_1_i2c_1 {
	ser_3: gmsl-serializer@42 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max96717";
		reg = <0x42>;

		// i2c addr alias map "sensor, gpio expander"
		i2c-addr-alias-map-local = <0x3e 0x27>;
		i2c-addr-alias-map-remote = <0x48 0x25>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&ser_3 0 0 11>;

		pinctrl-names = "default";
		pinctrl-0 = <&ser_3_pinmux_default>;

		ser_3_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-rx = <1>;
				maxim,gmsl-rx-id = <0>;
				output-enable;
				output-low;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			ser_3_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};
		};

		pipe@0 {
			reg = <0>;

			maxim,stream-id = <1>;
		};

		channel@0 {
			reg = <0>;
			label = "ser_3_ch_0";

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			clock-noncontinuous;
		};
	};
};

&ser_3_i2c_0 {
	pca9554_cam_3: pca9554@27 {
		compatible = "nxp,pca9554";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "EXPOSURE_TRIG_IN",
						"FLASH_OUT",
						"INFO_TRIG_IN",
						"CAMA_SHUTTER",
						"CSI_P1_nRST",
						"PWR_TIME0",
						"PWR_TIME1",
						"3V3_EN";
		vcc-supply = <&vdd_1v8_hs>;
		vcc_lp = "vcc";
		status = "okay";
	};

	cam_3: rpi22_tevs_d@3e {
		compatible = "tn,tevs";
		/* I2C device address */
		reg = <0x3e>;
		status = "okay";

		reset-gpios = <&pca9554_cam_3 4 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_cam_3 2 GPIO_ACTIVE_HIGH>;
		data-lanes = <CAM_LANES>;
		data-frequency = <800>;
		trigger-mode = <2>;
		// hw-reset;

		/* V4L2 device node location */
		devnode = "video3";
		/* Physical dimensions of sensor */
		physical_w = "3.680";
		physical_h = "2.760";
		sensor_model = "tevs";
		use_sensor_mode_id = "false";

		mode0 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "2464";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "21000000"; /* 21.0 fps */
			step_framerate = "1";
			default_framerate = "21000000"; /* 21.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode1 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "1848";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "28000000"; /* 28.0 fps */
			step_framerate = "1";
			default_framerate = "28000000"; /* 28.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode2 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1920";
			active_h = "1080";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 30.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 30.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode3 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1640";
			active_h = "1232";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode4 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1280";
			active_h = "720";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode5 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_c";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "640";
			active_h = "480";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;

				rpi22_tevs_3_out: endpoint {
					port-index = <2>;
					bus-width = <CAM_LANES>;
					remote-endpoint = <&csi_3_in>;
				};
			};
		};
	};
};

&des_2_i2c_0 {
	ser_4: gmsl-serializer@41 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max96717";
		reg = <0x41>;

		// i2c addr alias map "sensor, gpio expander"
		i2c-addr-alias-map-local = <0x3d 0x26>;
		i2c-addr-alias-map-remote = <0x48 0x25>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&ser_4 0 0 11>;

		pinctrl-names = "default";
		pinctrl-0 = <&ser_4_pinmux_default>;

		ser_4_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-rx = <1>;
				maxim,gmsl-rx-id = <0>;
				output-enable;
				output-low;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			ser_4_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};
		};

		pipe@0 {
			reg = <0>;

			maxim,stream-id = <0>;
		};

		channel@0 {
			reg = <0>;
			label = "ser_4_ch_0";

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			clock-noncontinuous;
		};
	};
};

&ser_4_i2c_0 {
	pca9554_cam_4: pca9554@26 {
		compatible = "nxp,pca9554";
		reg = <0x26>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "EXPOSURE_TRIG_IN",
						"FLASH_OUT",
						"INFO_TRIG_IN",
						"CAMA_SHUTTER",
						"CSI_P1_nRST",
						"PWR_TIME0",
						"PWR_TIME1",
						"3V3_EN";
		vcc-supply = <&vdd_1v8_hs>;
		vcc_lp = "vcc";
		status = "okay";
	};

	cam_4: rpi22_tevs_e@3d {
		compatible = "tn,tevs";
		/* I2C device address */
		reg = <0x3d>;
		status = "okay";

		reset-gpios = <&pca9554_cam_4 4 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_cam_4 2 GPIO_ACTIVE_HIGH>;
		data-lanes = <CAM_LANES>;
		data-frequency = <800>;
		trigger-mode = <2>;
		// hw-reset;

		/* V4L2 device node location */
		devnode = "video4";
		/* Physical dimensions of sensor */
		physical_w = "3.680";
		physical_h = "2.760";
		sensor_model = "tevs";
		use_sensor_mode_id = "false";

		mode0 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "2464";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "21000000"; /* 21.0 fps */
			step_framerate = "1";
			default_framerate = "21000000"; /* 21.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode1 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "1848";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "28000000"; /* 28.0 fps */
			step_framerate = "1";
			default_framerate = "28000000"; /* 28.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode2 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1920";
			active_h = "1080";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 30.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 30.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode3 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1640";
			active_h = "1232";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode4 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1280";
			active_h = "720";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode5 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "640";
			active_h = "480";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;

				rpi22_tevs_4_out: endpoint {
					port-index = <4>;
					bus-width = <CAM_LANES>;
					remote-endpoint = <&csi_4_in>;
				};
			};
		};
	};
};

&des_2_i2c_1 {
	ser_5: gmsl-serializer@42 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max96717";
		reg = <0x42>;

		// i2c addr alias map "sensor, gpio expander"
		i2c-addr-alias-map-local = <0x3e 0x27>;
		i2c-addr-alias-map-remote = <0x48 0x25>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&ser_5 0 0 11>;

		pinctrl-names = "default";
		pinctrl-0 = <&ser_5_pinmux_default>;

		ser_5_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-rx = <1>;
				maxim,gmsl-rx-id = <0>;
				output-enable;
				output-low;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			ser_5_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};
		};

		pipe@0 {
			reg = <0>;

			maxim,stream-id = <1>;
		};

		channel@0 {
			reg = <0>;
			label = "ser_5_ch_0";

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			clock-noncontinuous;
		};
	};
};

&ser_5_i2c_0 {
	pca9554_cam_5: pca9554@27 {
		compatible = "nxp,pca9554";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "EXPOSURE_TRIG_IN",
						"FLASH_OUT",
						"INFO_TRIG_IN",
						"CAMA_SHUTTER",
						"CSI_P1_nRST",
						"PWR_TIME0",
						"PWR_TIME1",
						"3V3_EN";
		vcc-supply = <&vdd_1v8_hs>;
		vcc_lp = "vcc";
		status = "okay";
	};

	cam_5: rpi22_tevs_f@3e {
		compatible = "tn,tevs";
		/* I2C device address */
		reg = <0x3e>;
		status = "okay";

		reset-gpios = <&pca9554_cam_5 4 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_cam_5 2 GPIO_ACTIVE_HIGH>;
		data-lanes = <CAM_LANES>;
		data-frequency = <800>;
		trigger-mode = <2>;
		// hw-reset;

		/* V4L2 device node location */
		devnode = "video5";
		/* Physical dimensions of sensor */
		physical_w = "3.680";
		physical_h = "2.760";
		sensor_model = "tevs";
		use_sensor_mode_id = "false";

		mode0 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "2464";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "21000000"; /* 21.0 fps */
			step_framerate = "1";
			default_framerate = "21000000"; /* 21.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode1 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "1848";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "28000000"; /* 28.0 fps */
			step_framerate = "1";
			default_framerate = "28000000"; /* 28.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode2 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1920";
			active_h = "1080";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 30.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 30.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode3 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1640";
			active_h = "1232";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode4 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1280";
			active_h = "720";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode5 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_e";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "640";
			active_h = "480";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;

				rpi22_tevs_5_out: endpoint {
					port-index = <4>;
					bus-width = <CAM_LANES>;
					remote-endpoint = <&csi_5_in>;
				};
			};
		};
	};
};

&des_3_i2c_0 {
	ser_6: gmsl-serializer@41 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max96717";
		reg = <0x41>;

		// i2c addr alias map "sensor, gpio expander"
		i2c-addr-alias-map-local = <0x3d 0x26>;
		i2c-addr-alias-map-remote = <0x48 0x25>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&ser_6 0 0 11>;

		pinctrl-names = "default";
		pinctrl-0 = <&ser_6_pinmux_default>;

		ser_6_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-rx = <1>;
				maxim,gmsl-rx-id = <0>;
				output-enable;
				output-low;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			ser_6_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};
		};

		pipe@0 {
			reg = <0>;

			maxim,stream-id = <0>;
		};

		channel@0 {
			reg = <0>;
			label = "ser_6_ch_0";

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			clock-noncontinuous;
		};
	};
};

&ser_6_i2c_0 {
	pca9554_cam_6: pca9554@26 {
		compatible = "nxp,pca9554";
		reg = <0x26>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "EXPOSURE_TRIG_IN",
						"FLASH_OUT",
						"INFO_TRIG_IN",
						"CAMA_SHUTTER",
						"CSI_P1_nRST",
						"PWR_TIME0",
						"PWR_TIME1",
						"3V3_EN";
		vcc-supply = <&vdd_1v8_hs>;
		vcc_lp = "vcc";
		status = "okay";
	};

	cam_6: rpi22_tevs_g@3d {
		compatible = "tn,tevs";
		/* I2C device address */
		reg = <0x3d>;
		status = "okay";

		reset-gpios = <&pca9554_cam_6 4 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_cam_6 2 GPIO_ACTIVE_HIGH>;
		data-lanes = <CAM_LANES>;
		data-frequency = <800>;
		trigger-mode = <2>;
		// hw-reset;

		/* V4L2 device node location */
		devnode = "video6";
		/* Physical dimensions of sensor */
		physical_w = "3.680";
		physical_h = "2.760";
		sensor_model = "tevs";
		use_sensor_mode_id = "false";

		mode0 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "2464";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "21000000"; /* 21.0 fps */
			step_framerate = "1";
			default_framerate = "21000000"; /* 21.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode1 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "1848";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "28000000"; /* 28.0 fps */
			step_framerate = "1";
			default_framerate = "28000000"; /* 28.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode2 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1920";
			active_h = "1080";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 30.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 30.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode3 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1640";
			active_h = "1232";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode4 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1280";
			active_h = "720";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode5 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "640";
			active_h = "480";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;

				rpi22_tevs_6_out: endpoint {
					port-index = <6>;
					bus-width = <CAM_LANES>;
					remote-endpoint = <&csi_6_in>;
				};
			};
		};
	};
};

&des_3_i2c_1 {
	ser_7: gmsl-serializer@42 {
		status = "okay";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "maxim,max96717";
		reg = <0x42>;

		// i2c addr alias map "sensor, gpio expander"
		i2c-addr-alias-map-local = <0x3e 0x27>;
		i2c-addr-alias-map-remote = <0x48 0x25>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&ser_7 0 0 11>;

		pinctrl-names = "default";
		pinctrl-0 = <&ser_7_pinmux_default>;

		ser_7_pinmux_default: pinmux {
			mfp0_fsync {
				pins = "mfp0";
				function = "gpio";
				maxim,gmsl-rx = <1>;
				maxim,gmsl-rx-id = <0>;
				output-enable;
				output-low;
			};
		};

		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			compatible = "i2c-mux-gpio";
			i2c-parent = <&cam_i2c>;
			mux-gpios = <&gpio_aon CAM_I2C_MUX GPIO_ACTIVE_HIGH>;

			ser_7_i2c_0: i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;

				reg = <0>;
			};
		};

		pipe@0 {
			reg = <0>;

			maxim,stream-id = <1>;
		};

		channel@0 {
			reg = <0>;
			label = "ser_7_ch_0";

			clock-lanes = <0>;
			data-lanes = <1 2 3 4>;
			clock-noncontinuous;
		};
	};
};

&ser_7_i2c_0 {
	pca9554_cam_7: pca9554@27 {
		compatible = "nxp,pca9554";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "EXPOSURE_TRIG_IN",
						"FLASH_OUT",
						"INFO_TRIG_IN",
						"CAMA_SHUTTER",
						"CSI_P1_nRST",
						"PWR_TIME0",
						"PWR_TIME1",
						"3V3_EN";
		vcc-supply = <&vdd_1v8_hs>;
		vcc_lp = "vcc";
		status = "okay";
	};

	cam_7: rpi22_tevs_h@3e {
		compatible = "tn,tevs";
		/* I2C device address */
		reg = <0x3e>;
		status = "okay";

		reset-gpios = <&pca9554_cam_7 4 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_cam_7 2 GPIO_ACTIVE_HIGH>;
		data-lanes = <CAM_LANES>;
		data-frequency = <800>;
		trigger-mode = <2>;
		// hw-reset;

		/* V4L2 device node location */
		devnode = "video7";
		/* Physical dimensions of sensor */
		physical_w = "3.680";
		physical_h = "2.760";
		sensor_model = "tevs";
		use_sensor_mode_id = "false";

		mode0 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "2464";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "21000000"; /* 21.0 fps */
			step_framerate = "1";
			default_framerate = "21000000"; /* 21.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode1 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "3280";
			active_h = "1848";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "28000000"; /* 28.0 fps */
			step_framerate = "1";
			default_framerate = "28000000"; /* 28.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode2 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1920";
			active_h = "1080";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 30.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 30.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode3 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1640";
			active_h = "1232";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "30000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "30000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode4 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "1280";
			active_h = "720";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		mode5 {
			mclk_khz = "24000";
			num_lanes = CAM_LANES_STRING;
			tegra_sinterface = "serial_g";
			phy_mode = "DPHY";
			discontinuous_clk = "yes";
			dpcm_enable = "false";
			cil_settletime = "0";

			active_w = "640";
			active_h = "480";
			mode_type = "yuv";
			pixel_phase = "uyvy";
			csi_pixel_bit_depth = "16";
			readout_orientation = "90";
			line_length = "3448";
			inherent_gain = "1";
			mclk_multiplier = "9.33";
			pix_clk_hz = "200000000";
			serdes_pix_clk_hz = "500000000";

			gain_factor = "16";
			framerate_factor = "1000000";
			exposure_factor = "1000000";
			min_gain_val = "16"; /* 1.00x */
			max_gain_val = "170"; /* 10.66x */
			step_gain_val = "1";
			default_gain = "16"; /* 1.00x */
			min_hdr_ratio = "1";
			max_hdr_ratio = "1";
			min_framerate = "2000000"; /* 2.0 fps */
			max_framerate = "60000000"; /* 60.0 fps */
			step_framerate = "1";
			default_framerate = "60000000"; /* 60.0 fps */
			min_exp_time = "13"; /* us */
			max_exp_time = "683709"; /* us */
			step_exp_time = "1";
			default_exp_time = "2495"; /* us */
			embedded_metadata_height = "0";
		};
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;

				rpi22_tevs_7_out: endpoint {
					port-index = <6>;
					bus-width = <CAM_LANES>;
					remote-endpoint = <&csi_7_in>;
				};
			};
		};
	};
};


