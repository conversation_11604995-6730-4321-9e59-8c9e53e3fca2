// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

#include <dt-bindings/input/linux-event-codes.h>
#include <dt-bindings/input/gpio-keys.h>

#include "tegra234-p3701-0000.dtsi"
#include "tegra234-p3737-0000.dtsi"

/ {
	model = "NVIDIA Jetson AGX Orin Developer Kit";
	compatible = "nvidia,p3737-0000+p3701-0000", "nvidia,p3701-0000", "nvidia,tegra234";

	aliases {
		serial0 = &tcu;
		serial1 = &uarta;
	};

	chosen {
		bootargs = "console=ttyTCU0,115200n8";
		stdout-path = "serial0:115200n8";
	};

	bus@0 {
		serial@3100000 {
			compatible = "nvidia,tegra194-hsuart";
			reset-names = "serial";
			status = "okay";
		};

		serial@31d0000 {
			current-speed = <115200>;
			status = "okay";
		};

		pwm@32a0000 {
			assigned-clocks = <&bpmp TEGRA234_CLK_PWM3>;
			assigned-clock-parents = <&bpmp TEGRA234_CLK_PLLP_OUT0>;
			status = "okay";
		};

		hda@3510000 {
			nvidia,model = "NVIDIA Jetson AGX Orin HDA";
			status = "okay";
		};

		padctl@3520000 {
			status = "okay";

			pads {
				usb2 {
					lanes {
						usb2-0 {
							status = "okay";
						};

						usb2-1 {
							status = "okay";
						};

						usb2-2 {
							status = "okay";
						};

						usb2-3 {
							status = "okay";
						};
					};
				};

				usb3 {
					lanes {
						usb3-0 {
							status = "okay";
						};

						usb3-1 {
							status = "okay";
						};

						usb3-2 {
							status = "okay";
						};
					};
				};
			};

			ports {
				usb2-0 {
					mode = "otg";
					usb-role-switch;
					status = "okay";
					port {
						hs_typec_p1: endpoint {
							remote-endpoint = <&hs_ucsi_ccg_p1>;
						};
					};
				};

				usb2-1 {
					mode = "host";
					status = "okay";
					port {
						hs_typec_p0: endpoint {
							remote-endpoint = <&hs_ucsi_ccg_p0>;
						};
					};
				};

				usb2-2 {
					mode = "host";
					status = "okay";
				};

				usb2-3 {
					mode = "host";
					status = "okay";
				};

				usb3-0 {
					nvidia,usb2-companion = <1>;
					status = "okay";
					port {
						ss_typec_p0: endpoint {
							remote-endpoint = <&ss_ucsi_ccg_p0>;
						};
					};
				};

				usb3-1 {
					nvidia,usb2-companion = <0>;
					status = "okay";
					port {
						ss_typec_p1: endpoint {
							remote-endpoint = <&ss_ucsi_ccg_p1>;
						};
					};
				};

				usb3-2 {
					nvidia,usb2-companion = <3>;
					status = "okay";
				};
			};
		};

		usb@3550000 {
			status = "okay";

			phys = <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-1}>;
			phy-names = "usb2-0", "usb3-0";
		};

		usb@3610000 {
			status = "okay";

			phys = <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-1}>,
			       <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-2}>,
			       <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-3}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-1}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-2}>;
			phy-names = "usb2-0", "usb2-1", "usb2-2", "usb2-3",
				    "usb3-0", "usb3-1", "usb3-2";
		};

		ethernet@6800000 {
			status = "okay";

			phy-handle = <&mgbe0_phy>;
			phy-mode = "10gbase-r";

			mdio {
				#address-cells = <1>;
				#size-cells = <0>;

				mgbe0_phy: phy@0 {
					compatible = "ethernet-phy-ieee802.3-c45";
					reg = <0x0>;

					#phy-cells = <0>;
				};
			};
		};

		i2c@c240000 {
			status = "okay";

			typec@8 {
				compatible = "cypress,cypd4226";
				reg = <0x08>;
				interrupt-parent = <&gpio>;
				interrupts = <TEGRA234_MAIN_GPIO(Y, 4) IRQ_TYPE_LEVEL_LOW>;
				firmware-name = "nvidia,jetson-agx-xavier";
				status = "okay";

				#address-cells = <1>;
				#size-cells = <0>;

				ccg_typec_con0: connector@0 {
					compatible = "usb-c-connector";
					reg = <0>;
					label = "USB-C";
					data-role = "host";

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;
							hs_ucsi_ccg_p0: endpoint {
								remote-endpoint = <&hs_typec_p0>;
							};
						};

						port@1 {
							reg = <1>;
							ss_ucsi_ccg_p0: endpoint {
								remote-endpoint = <&ss_typec_p0>;
							};
						};
					};
				};

				ccg_typec_con1: connector@1 {
					compatible = "usb-c-connector";
					reg = <1>;
					label = "USB-C";
					data-role = "dual";

					ports {
						#address-cells = <1>;
						#size-cells = <0>;

						port@0 {
							reg = <0>;
							hs_ucsi_ccg_p1: endpoint {
								remote-endpoint = <&hs_typec_p1>;
							};
						};

						port@1 {
							reg = <1>;
							ss_ucsi_ccg_p1: endpoint {
								remote-endpoint = <&ss_typec_p1>;
							};
						};
					};
				};
			};
		};

		pcie@14100000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;

			phys = <&p2u_hsio_3>;
			phy-names = "p2u-0";
		};

		pcie@14160000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;

			phys = <&p2u_hsio_4>, <&p2u_hsio_5>, <&p2u_hsio_6>,
			       <&p2u_hsio_7>;
			phy-names = "p2u-0", "p2u-1", "p2u-2", "p2u-3";
		};

		pcie@141a0000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ls>;
			vpcie3v3-supply = <&vdd_3v3_pcie>;
			vpcie12v-supply = <&vdd_12v_pcie>;

			phys = <&p2u_nvhs_0>, <&p2u_nvhs_1>, <&p2u_nvhs_2>,
			       <&p2u_nvhs_3>, <&p2u_nvhs_4>, <&p2u_nvhs_5>,
			       <&p2u_nvhs_6>, <&p2u_nvhs_7>;
			phy-names = "p2u-0", "p2u-1", "p2u-2", "p2u-3", "p2u-4",
				    "p2u-5", "p2u-6", "p2u-7";
		};

		pcie-ep@141a0000 {
			status = "disabled";

			vddio-pex-ctl-supply = <&vdd_1v8_ls>;

			reset-gpios = <&gpio TEGRA234_MAIN_GPIO(AF, 1) GPIO_ACTIVE_LOW>;

			nvidia,refclk-select-gpios = <&gpio_aon
						      TEGRA234_AON_GPIO(AA, 4)
						      GPIO_ACTIVE_HIGH>;

			phys = <&p2u_nvhs_0>, <&p2u_nvhs_1>, <&p2u_nvhs_2>,
			       <&p2u_nvhs_3>, <&p2u_nvhs_4>, <&p2u_nvhs_5>,
			       <&p2u_nvhs_6>, <&p2u_nvhs_7>;
			phy-names = "p2u-0", "p2u-1", "p2u-2", "p2u-3", "p2u-4",
				    "p2u-5", "p2u-6", "p2u-7";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		status = "okay";

		key-force-recovery {
			label = "Force Recovery";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 0) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <BTN_1>;
		};

		key-power {
			label = "Power";
			gpios = <&gpio_aon TEGRA234_AON_GPIO(EE, 4) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_POWER>;
			wakeup-event-action = <EV_ACT_ASSERTED>;
			wakeup-source;
		};

		key-suspend {
			label = "Suspend";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 2) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_SLEEP>;
		};
	};

	pwm-fan {
		cooling-levels = <66 215 255>;
	};

	serial {
		status = "okay";
	};

	sound {
		compatible = "nvidia,tegra186-audio-graph-card";
		status = "okay";

		dais = /* ADMAIF (FE) Ports */
		       <&admaif0_port>, <&admaif1_port>, <&admaif2_port>, <&admaif3_port>,
		       <&admaif4_port>, <&admaif5_port>, <&admaif6_port>, <&admaif7_port>,
		       <&admaif8_port>, <&admaif9_port>, <&admaif10_port>, <&admaif11_port>,
		       <&admaif12_port>, <&admaif13_port>, <&admaif14_port>, <&admaif15_port>,
		       <&admaif16_port>, <&admaif17_port>, <&admaif18_port>, <&admaif19_port>,
		       /* XBAR Ports */
		       <&xbar_i2s1_port>, <&xbar_i2s2_port>, <&xbar_i2s4_port>,
		       <&xbar_i2s6_port>, <&xbar_dmic3_port>,
		       <&xbar_sfc1_in_port>, <&xbar_sfc2_in_port>,
		       <&xbar_sfc3_in_port>, <&xbar_sfc4_in_port>,
		       <&xbar_mvc1_in_port>, <&xbar_mvc2_in_port>,
		       <&xbar_amx1_in1_port>, <&xbar_amx1_in2_port>,
		       <&xbar_amx1_in3_port>, <&xbar_amx1_in4_port>,
		       <&xbar_amx2_in1_port>, <&xbar_amx2_in2_port>,
		       <&xbar_amx2_in3_port>, <&xbar_amx2_in4_port>,
		       <&xbar_amx3_in1_port>, <&xbar_amx3_in2_port>,
		       <&xbar_amx3_in3_port>, <&xbar_amx3_in4_port>,
		       <&xbar_amx4_in1_port>, <&xbar_amx4_in2_port>,
		       <&xbar_amx4_in3_port>, <&xbar_amx4_in4_port>,
		       <&xbar_adx1_in_port>, <&xbar_adx2_in_port>,
		       <&xbar_adx3_in_port>, <&xbar_adx4_in_port>,
		       <&xbar_mix_in1_port>, <&xbar_mix_in2_port>,
		       <&xbar_mix_in3_port>, <&xbar_mix_in4_port>,
		       <&xbar_mix_in5_port>, <&xbar_mix_in6_port>,
		       <&xbar_mix_in7_port>, <&xbar_mix_in8_port>,
		       <&xbar_mix_in9_port>, <&xbar_mix_in10_port>,
		       <&xbar_asrc_in1_port>, <&xbar_asrc_in2_port>,
		       <&xbar_asrc_in3_port>, <&xbar_asrc_in4_port>,
		       <&xbar_asrc_in5_port>, <&xbar_asrc_in6_port>,
		       <&xbar_asrc_in7_port>,
		       <&xbar_ope1_in_port>,
		       /* HW accelerators */
		       <&sfc1_out_port>, <&sfc2_out_port>,
		       <&sfc3_out_port>, <&sfc4_out_port>,
		       <&mvc1_out_port>, <&mvc2_out_port>,
		       <&amx1_out_port>, <&amx2_out_port>,
		       <&amx3_out_port>, <&amx4_out_port>,
		       <&adx1_out1_port>, <&adx1_out2_port>,
		       <&adx1_out3_port>, <&adx1_out4_port>,
		       <&adx2_out1_port>, <&adx2_out2_port>,
		       <&adx2_out3_port>, <&adx2_out4_port>,
		       <&adx3_out1_port>, <&adx3_out2_port>,
		       <&adx3_out3_port>, <&adx3_out4_port>,
		       <&adx4_out1_port>, <&adx4_out2_port>,
		       <&adx4_out3_port>, <&adx4_out4_port>,
		       <&mix_out1_port>, <&mix_out2_port>, <&mix_out3_port>,
		       <&mix_out4_port>, <&mix_out5_port>,
		       <&asrc_out1_port>, <&asrc_out2_port>, <&asrc_out3_port>,
		       <&asrc_out4_port>, <&asrc_out5_port>, <&asrc_out6_port>,
		       <&ope1_out_port>,
		       /* BE I/O Ports */
		       <&i2s1_port>, <&i2s2_port>, <&i2s4_port>, <&i2s6_port>,
		       <&dmic3_port>;

		label = "NVIDIA Jetson AGX Orin APE";

		widgets = "Microphone",	"CVB-RT MIC Jack",
			  "Microphone",	"CVB-RT MIC",
			  "Headphone",	"CVB-RT HP Jack",
			  "Speaker",	"CVB-RT SPK";

		routing = /* I2S1 <-> RT5640 */
			  "CVB-RT AIF1 Playback",	"I2S1 DAP-Playback",
			  "I2S1 DAP-Capture",		"CVB-RT AIF1 Capture",
			  /* RT5640 codec controls */
			  "CVB-RT HP Jack",		"CVB-RT HPOL",
			  "CVB-RT HP Jack",		"CVB-RT HPOR",
			  "CVB-RT IN1P",		"CVB-RT MIC Jack",
			  "CVB-RT IN2P",		"CVB-RT MIC Jack",
			  "CVB-RT SPK",			"CVB-RT SPOLP",
			  "CVB-RT SPK",			"CVB-RT SPORP",
			  "CVB-RT DMIC1",		"CVB-RT MIC",
			  "CVB-RT DMIC2",		"CVB-RT MIC";
	};

	thermal-zones {
		tj-thermal {
			cooling-maps {
				map-active-0 {
					cooling-device = <&fan 0 1>;
					trip = <&tj_trip_active0>;
				};

				map-active-1 {
					cooling-device = <&fan 1 2>;
					trip = <&tj_trip_active1>;
				};
			};
		};
	};
};
