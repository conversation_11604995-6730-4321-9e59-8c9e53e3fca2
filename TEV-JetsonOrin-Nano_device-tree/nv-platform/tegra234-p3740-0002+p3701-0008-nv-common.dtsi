// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2023-2024, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

#include "nv-soc/tegra234-overlay.dtsi"
#include "nv-soc/tegra234-soc-thermal.dtsi"
#include "nv-soc/tegra234-soc-thermal-slowdown-cluster.dtsi"
#include "nv-soc/tegra234-soc-thermal-shutdown.dtsi"
#include "nv-soc/tegra234-soc-thermal-trip-event.dtsi"
#include "nv-soc/tegra234-soc-camera.dtsi"
#include "tegra234-camera-p3785.dtsi"
#include "tegra234-p3740-0002.dtsi"
#include "tegra234-p3701-0008.dtsi"
#include "tegra234-dcb-p3737-0000-p3701-0000.dtsi"

/ {
	aliases {
		serial2 = "/bus@0/serial@3110000";
	};

	chosen {
		bootargs = "console=ttyTCU0,115200n8";
	};

	bpmp {
		thermal {
			status = "okay";
		};
	};

	cpus {
		idle-states {
			c7 {
				status = "okay";
			};
		};
	};

	nvpmodel {
		status = "okay";
	};

	soctherm-oc-event {
		status = "okay";
	};

	thermal-zones {
		cpu-thermal {
			status = "okay";

			cooling-maps {
				map-hot-surface-alert {
					cooling-device = <&hot_surface_alert 0 0>;
				};
			};
		};

		cv0-thermal {
			status = "okay";

			cooling-maps {
				map-hot-surface-alert {
					cooling-device = <&hot_surface_alert 0 0>;
				};
			};
		};

		cv1-thermal {
			status = "okay";

			cooling-maps {
				map-hot-surface-alert {
					cooling-device = <&hot_surface_alert 0 0>;
				};
			};
		};

		cv2-thermal {
			status = "okay";

			cooling-maps {
				map-hot-surface-alert {
					cooling-device = <&hot_surface_alert 0 0>;
				};
			};
		};

		gpu-thermal {
			status = "okay";

			cooling-maps {
				map-hot-surface-alert {
					cooling-device = <&hot_surface_alert 0 0>;
				};
			};
		};

		soc0-thermal {
			status = "okay";

			cooling-maps {
				map-hot-surface-alert {
					cooling-device = <&hot_surface_alert 0 0>;
				};
			};
		};

		soc1-thermal {
			status = "okay";

			cooling-maps {
				map-hot-surface-alert {
					cooling-device = <&hot_surface_alert 0 0>;
				};
			};
		};

		soc2-thermal {
			status = "okay";

			cooling-maps {
				map-hot-surface-alert {
					cooling-device = <&hot_surface_alert 0 0>;
				};
			};
		};

		tj-thermal {
			status = "okay";
		};
	};

	bus@0 {
		smmu_test {
			compatible = "nvidia,smmu_test";
			iommus = <&smmu_niso0 TEGRA234_SID_SMMU_TEST>;
			status = "okay";
		};

		pinmux@2430000 {
			status = "okay";
		};

		serial@3110000 {
			compatible = "nvidia,tegra194-hsuart";
			reset-names = "serial";
			status = "okay";
		};

		serial@31d0000 {
			current-speed = <115200>;
			status = "okay";                        
		};

		tachometer@39c0000 {
			status = "okay";
		};

		hsp@3c00000 {
			status = "okay";
		};

		hsp@c150000 {
			status = "okay";
		};

		mttcan@c310000 {
			status = "okay";
		};

		mttcan@c320000 {
			status = "okay";
		};

		actmon@d230000 {
			status = "okay";
		};

		hwpm@f100000 {
			status = "okay";
		};

		mc-hwpm@2c10000 {
			status = "okay";
		};

		aconnect@2900000 {
			ahub@2900800 {
				i2s@2901200 {
					status = "okay";
				};

				i2s@2901400 {
					status = "okay";
				};

				dmic@2904000 {
					status = "okay";
				};

				dmic@2904100 {
					status = "okay";
				};


				dmic@2904300 {
					status = "okay";
				};

				dspk@2905000 {
					status = "okay";
				};

				dspk@2905100 {
					status = "okay";
				};

				arad@290e400 {
					status = "okay";
				};

				afc@2907000 {
					status = "okay";
				};

				afc@2907100 {
					status = "okay";
				};

				afc@2907200 {
					status = "okay";
				};

				afc@2907300 {
					status = "okay";
				};

				afc@2907400 {
					status = "okay";
				};

				afc@2907500 {
					status = "okay";
				};
			};
		};

		host1x@13e00000 {
			nvjpg@15380000 {
				status = "okay";
			};

			nvenc@154c0000 {
				status = "okay";
			};

			tsec@15500000 {
				status = "okay";
			};

			nvjpg@15540000 {
				status = "okay";
			};

			crypto@15810000 {
				status = "okay";
			};

			crypto@15820000 {
				status = "okay";
			};

			crypto@15840000 {
				status = "okay";
			};

			nvdla0@15880000 {
				status = "okay";
			};

			nvdla1@158c0000 {
				status = "okay";
			};

			ofa@15a50000 {
				status = "okay";
			};

			pva0@16000000 {
				status = "okay";

				pva0_niso1_ctx0 {
					status = "okay";
				};

				pva0_niso1_ctx1 {
					status = "okay";
				};

				pva0_niso1_ctx2 {
					status = "okay";
				};

				pva0_niso1_ctx3 {
					status = "okay";
				};

				pva0_niso1_ctx4 {
					status = "okay";
				};

				pva0_niso1_ctx5 {
					status = "okay";
				};

				pva0_niso1_ctx6 {
					status = "okay";
				};

				pva0_niso1_ctx7 {
					status = "okay";
				};
			};
		};

		pcie@141a0000 {
			reg = <0x00 0x141a0000 0x0 0x00020000   /* appl registers (128K)      */
			       0x00 0x3a000000 0x0 0x00040000   /* configuration space (256K) */
			       0x00 0x3a040000 0x0 0x00040000   /* iATU_DMA reg space (256K)  */
			       0x00 0x3a080000 0x0 0x00040000   /* DBI reg space (256K)       */
			       0x2e 0x20000000 0x0 0x10000000>; /* ECAM (256MB)               */

			ranges = <0x81000000 0x00 0x3a100000 0x00 0x3a100000 0x0 0x00100000      /* downstream I/O (1MB) */
			          0x82000000 0x00 0x40000000 0x2e 0x30000000 0x0 0x08000000      /* non-prefetchable memory (128MB) */
			          0xc3000000 0x28 0x00000000 0x28 0x00000000 0x6 0x20000000>;    /* prefetchable memory (25088MB) */
		};

		gpu@17000000 {
			status = "okay";
		};
	};

	tegra-hsp@b950000 {
		status = "okay";
	};

	dce@d800000 {
		status = "okay";
	};

	tegra_mce@e100000 {
		status = "okay";
	};

	display@13800000 {
		status = "okay";
	};
};
