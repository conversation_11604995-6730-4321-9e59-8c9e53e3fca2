// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2017-2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

#include <dt-bindings/clock/tegra234-clock.h>

#define CAM0_PWDN	TEGRA234_AON_GPIO(AA, 4)

/ {
	gpio@c2f0000 {
		camera-control-output-high {
			gpio-hog;
			output-high;
			gpios = <CAM0_PWDN 0>;
			label = "cam0-pwdn";
		};
	};

	tegra-capture-vi {
		nvidia,vi-mapping =
			<0 0>,
			<1 0>,
			<2 0>,
			<3 0>,
			<4 1>,
			<5 1>;
		num-channels = <1>;
		ports {
			#address-cells = <1>;
			#size-cells = <0>;
			port@0 {
				reg = <0>;
				p3785_vi_in0: endpoint {
					port-index = <0>;
					bus-width = <8>;
					remote-endpoint = <&p3785_csi_out0>;
				};
			};
		};
	};
	bus@0 {
		host1x@13e00000 {
			nvcsi@15a00000 {
				num-channels = <1>;
				#address-cells = <1>;
				#size-cells = <0>;
				channel@0 {
					reg = <0>;
					ports {
						#address-cells = <1>;
						#size-cells = <0>;
						port@0 {
							reg = <0>;
							p3785_csi_in0: endpoint@0 {
								port-index = <0>;
								bus-width = <8>;
								remote-endpoint = <&p3785_out0>;
							};
						};
						port@1 {
							reg = <1>;
							p3785_csi_out0: endpoint@1 {
								remote-endpoint = <&p3785_vi_in0>;
							};
						};
					};
				};
			};
		};

		i2c@3180000 {
			p3785@2b {
				compatible = "nvidia,lt6911uxc";
				/* I2C device address */
				reg = <0x2b>;

				/* V4L2 device node location */
				devnode = "video0";

				/* Physical dimensions of sensor */
				physical_w = "3.674";
				physical_h = "2.738";

				sensor_model = "p3785";

				/* Define any required hw resources needed by driver */
				/* ie. clocks, io pins, power sources
				avdd-reg = "vana";
				iovdd-reg = "vif";
				dvdd-reg = "vdig";*/

				/* Defines number of frames to be dropped by driver internally after applying */
				/* sensor crop settings. Some sensors send corrupt frames after applying */
				/* crop co-ordinates */
				/*post_crop_frame_drop = "0";*/

				/* Define any required hw resources needed by driver */
				/* ie. clocks, io pins, power sources */
				clocks = <&bpmp TEGRA234_CLK_EXTPERIPH1>,
					<&bpmp TEGRA234_CLK_EXTPERIPH1>;
				clock-names = "extperiph1", "pllp_grtba";
				mclk = "extperiph1";
				reset-gpios = <&gpio_aon CAM0_PWDN GPIO_ACTIVE_HIGH>;

				/**
				* ==== Modes ====
				* A modeX node is required to support v4l2 driver
				* implementation with NVIDIA camera software stack
				*
				* == Signal properties ==
				*
				* phy_mode = "";
				* PHY mode used by the MIPI lanes for this device
				*
				* tegra_sinterface = "";
				* CSI Serial interface connected to tegra
				* Incase of virtual HW devices, use virtual
				* For SW emulated devices, use host
				*
				* pix_clk_hz = "";
				* Sensor pixel clock used for calculations like exposure and framerate
				*
				* readout_orientation = "0";
				* Based on camera module orientation.
				* Only change readout_orientation if you specifically
				* Program a different readout order for this mode
				*
				* == Image format Properties ==
				*
				* active_w = "";
				* Pixel active region width
				*
				* active_h = "";
				* Pixel active region height
				*
				* pixel_t = "";
				* The sensor readout pixel pattern
				*
				* line_length = "";
				* Pixel line length (width) for sensor mode.
				*
				* == Source Control Settings ==
				*
				* Gain factor used to convert fixed point integer to float
				* Gain range [min_gain/gain_factor, max_gain/gain_factor]
				* Gain step [step_gain/gain_factor is the smallest step that can be configured]
				* Default gain [Default gain to be initialized for the control.
						*     use min_gain_val as default for optimal results]
				* Framerate factor used to convert fixed point integer to float
				* Framerate range [min_framerate/framerate_factor, max_framerate/framerate_factor]
				* Framerate step [step_framerate/framerate_factor is the smallest step that can be configured]
				* Default Framerate [Default framerate to be initialized for the control.
						*     use max_framerate to get required performance]
				* Exposure factor used to convert fixed point integer to float
				* For convenience use 1 sec = 1000000us as conversion factor
				* Exposure range [min_exp_time/exposure_factor, max_exp_time/exposure_factor]
				* Exposure step [step_exp_time/exposure_factor is the smallest step that can be configured]
				* Default Exposure Time [Default exposure to be initialized for the control.
						*     Set default exposure based on the default_framerate for optimal exposure settings]
				* For convenience use 1 sec = 1000000us as conversion factor
				*
				* gain_factor = ""; (integer factor used for floating to fixed point conversion)
				* min_gain_val = ""; (ceil to integer)
				* max_gain_val = ""; (ceil to integer)
				* step_gain_val = ""; (ceil to integer)
				* default_gain = ""; (ceil to integer)
				* Gain limits for mode
				*
				* exposure_factor = ""; (integer factor used for floating to fixed point conversion)
				* min_exp_time = ""; (ceil to integer)
				* max_exp_time = ""; (ceil to integer)
				* step_exp_time = ""; (ceil to integer)
				* default_exp_time = ""; (ceil to integer)
				* Exposure Time limits for mode (sec)
				*
				* framerate_factor = ""; (integer factor used for floating to fixed point conversion)
				* min_framerate = ""; (ceil to integer)
				* max_framerate = ""; (ceil to integer)
				* step_framerate = ""; (ceil to integer)
				* default_framerate = ""; (ceil to integer)
				* Framerate limits for mode (fps)
				*
				* embedded_metadata_height = "";
				* Sensor embedded metadata height in units of rows.
				* If sensor does not support embedded metadata value should be 0.

				* num_of_exposure = "";
				* Digital overlap(Dol) frames
				*
				* num_of_ignored_lines = "";
				* Used for cropping, eg. OB lines + Ignored area of effective pixel lines
				*
				* num_of_lines_offset_0 = "";
				* Used for cropping, vertical blanking in front of short exposure data
				* If more Dol frames are used, it can be extended, eg. num_of_lines_offset_1
				*
				* num_of_ignored_pixels = "";
				* Used for cropping, The length of line info(pixels)
				*
				* num_of_left_margin_pixels = "";
				* Used for cropping, the size of the left edge margin before
				* the active pixel area (after ignored pixels)
				*
				* num_of_right_margin_pixels = "";
				* Used for cropping, the size of the right edge margin after
				* the active pixel area
				*
				*/
				mode0 { // E2832_1920x1080_60Fps
					mclk_khz = "24000";
					num_lanes = "4";
					tegra_sinterface = "serial_a";
					phy_mode = "DPHY";
					discontinuous_clk = "yes";
					dpcm_enable = "false";
					cil_settletime = "0";

					active_w = "1920";
					active_h = "1080";
					mode_type = "rgb";
					pixel_phase = "rgb888";
					csi_pixel_bit_depth = "24";
					readout_orientation = "0";
					line_length = "1920";
					inherent_gain = "1";
					mclk_multiplier = "24";
					pix_clk_hz = "250000000";

					gain_factor = "16";
					framerate_factor = "1000000";
					exposure_factor = "1000000";
					min_gain_val = "16"; /* 1.00x */
					max_gain_val = "170"; /* 10.66x */
					step_gain_val = "1";
					default_gain = "16"; /* 1.00x */
					min_hdr_ratio = "1";
					max_hdr_ratio = "1";
					min_framerate = "2000000"; /* 2.0 fps */
					max_framerate = "60000000"; /* 60.0 fps */
					step_framerate = "1";
					default_framerate = "60000000"; /* 60.0 fps */
					min_exp_time = "13"; /* us */
					max_exp_time = "683709"; /* us */
					step_exp_time = "1";
					default_exp_time = "16667"; /* us  */
				};
				mode1 { // E2832_3840x2160
					mclk_khz = "24000";
					num_lanes = "8";
					tegra_sinterface = "serial_a";
					phy_mode = "DPHY";
					discontinuous_clk = "yes";
					dpcm_enable = "false";
					cil_settletime = "0";

					active_w = "3840";
					active_h = "2160";
					mode_type = "rgb";
					pixel_phase = "rgb888";
					csi_pixel_bit_depth = "24";
					readout_orientation = "0";
					line_length = "3840";
					inherent_gain = "1";
					mclk_multiplier = "24";
					pix_clk_hz = "500000000";

					gain_factor = "16";
					framerate_factor = "1000000";
					exposure_factor = "1000000";
					min_gain_val = "16"; /* 1.00x */
					max_gain_val = "170"; /* 10.66x */
					step_gain_val = "1";
					default_gain = "16"; /* 1.00x */
					min_hdr_ratio = "1";
					max_hdr_ratio = "1";
					min_framerate = "2000000"; /* 2.0 fps */
					max_framerate = "60000000"; /* 60.0 fps */
					step_framerate = "1";
					default_framerate = "60000000"; /* 60.0 fps */
					min_exp_time = "13"; /* us */
					max_exp_time = "683709"; /* us */
					step_exp_time = "1";
					default_exp_time = "16667"; /* us  */
				};

				mode2 { // E2832_1280x720_60Fps
					mclk_khz = "24000";
					num_lanes = "4";
					tegra_sinterface = "serial_a";
					phy_mode = "DPHY";
					discontinuous_clk = "yes";
					dpcm_enable = "false";
					cil_settletime = "0";

					active_w = "1280";
					active_h = "720";
					mode_type = "rgb";
					pixel_phase = "rgb888";
					csi_pixel_bit_depth = "24";
					readout_orientation = "0";
					line_length = "1280";
					inherent_gain = "1";
					mclk_multiplier = "24";
					pix_clk_hz = "250000000";

					gain_factor = "16";
					framerate_factor = "1000000";
					exposure_factor = "1000000";
					min_gain_val = "16"; /* 1.00x */
					max_gain_val = "170"; /* 10.66x */
					step_gain_val = "1";
					default_gain = "16"; /* 1.00x */
					min_hdr_ratio = "1";
					max_hdr_ratio = "1";
					min_framerate = "2000000"; /* 2.0 fps */
					max_framerate = "60000000"; /* 60.0 fps */
					step_framerate = "1";
					default_framerate = "60000000"; /* 60.0 fps */
					min_exp_time = "13"; /* us */
					max_exp_time = "683709"; /* us */
					step_exp_time = "1";
					default_exp_time = "16667"; /* us  */
				};

				ports {
					#address-cells = <1>;
					#size-cells = <0>;
					port@0 {
						reg = <0>;
						p3785_out0: endpoint {
							port-index = <0>;
							bus-width = <8>;
							remote-endpoint = <&p3785_csi_in0>;
						};
					};
				};
			};
		};
	};

	tegra-camera-platform {
		compatible = "nvidia, tegra-camera-platform";
		/**
		* Physical settings to calculate max ISO BW
		*
		* num_csi_lanes = <>;
		* Total number of CSI lanes when all cameras are active
		*
		* max_lane_speed = <>;
		* Max lane speed in Kbit/s
		*
		* min_bits_per_pixel = <>;
		* Min bits per pixel
		*
		* vi_peak_byte_per_pixel = <>;
		* Max byte per pixel for the VI ISO case
		*
		* vi_bw_margin_pct = <>;
		* Vi bandwidth margin in percentage
		*
		* max_pixel_rate = <>;
		* Max pixel rate in Kpixel/s for the ISP ISO case
		*
		* isp_peak_byte_per_pixel = <>;
		* Max byte per pixel for the ISP ISO case
		*
		* isp_bw_margin_pct = <>;
		* Isp bandwidth margin in percentage
		*/
		num_csi_lanes = <4>;
		max_lane_speed = <1500000>;
		min_bits_per_pixel = <10>;
		vi_peak_byte_per_pixel = <2>;
		vi_bw_margin_pct = <25>;
		max_pixel_rate = <750000>;
		isp_peak_byte_per_pixel = <5>;
		isp_bw_margin_pct = <25>;

		/**
		* The general guideline for naming badge_info contains 3 parts, and is as follows,
		* The first part is the camera_board_id for the module; if the module is in a FFD
		* platform, then use the platform name for this part.
		* The second part contains the position of the module, ex. "rear" or "front".
		* The third part contains the last 6 characters of a part number which is found
		* in the module's specsheet from the vender.
		*/
		modules {
			module0 {
				badge = "p3785_ltx6911";
				position = "bottom";
				orientation = "1";
				drivernode0 {
					/* Declare PCL support driver (classically known as guid)  */
					pcl_id = "v4l2_sensor";
					/* Declare the device-tree hierarchy to driver instance */
					sysfs-device-tree = "/sys/firmware/devicetree/base/bus@0/i2c@3180000/p3785@2b";
				};
			};
		};
	};
};
