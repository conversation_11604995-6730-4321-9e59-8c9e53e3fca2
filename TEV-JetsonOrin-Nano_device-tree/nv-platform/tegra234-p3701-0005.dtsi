// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

#include "tegra234-p3701-0000.dtsi"

/ {
	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		linux,cma { /* Needed for nvgpu comptags */
			compatible = "shared-dma-pool";
			reusable;
			size = <0x0 0x20000000>; /* 512MB */
			alignment = <0x0 0x10000>;
			linux,cma-default;
			status = "okay";
		};
	};
};
