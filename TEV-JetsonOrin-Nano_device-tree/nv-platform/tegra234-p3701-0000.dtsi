// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2022-2024, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

#include "tegra234-p3701-0000-prod-overlay.dtsi"

/ {
	bus@0 {
		i2c@c240000 {
			ina3221@40 {
				compatible = "ti,ina3221";
				reg = <0x40>;
				#address-cells = <1>;
				#size-cells = <0>;
				channel@0 {
					reg = <0x0>;
					label = "VDD_GPU_SOC";
					shunt-resistor-micro-ohms = <2000>;
				};
				channel@1 {
					reg = <0x1>;
					label = "VDD_CPU_CV";
					shunt-resistor-micro-ohms = <2000>;
				};
				channel@2 {
					reg = <0x2>;
					label = "VIN_SYS_5V0";
					shunt-resistor-micro-ohms = <2000>;
					ti,summation-disable;
				};
			};

			ina3221@41 {
				compatible = "ti,ina3221";
				reg = <0x41>;
				#address-cells = <1>;
				#size-cells = <0>;
				channel@0 {
					reg = <0x0>;
					status = "disabled";
				};
				channel@1 {
					reg = <0x1>;
					label = "VDDQ_VDD2_1V8AO";
					shunt-resistor-micro-ohms = <2000>;
				};
				channel@2 {
					reg = <0x2>;
					status = "disabled";
				};
			};
		};

		spi@3270000 {
			flash@0 {
				spi-max-frequency = <51000000>;
				spi-tx-bus-width = <1>;
				spi-rx-bus-width = <1>;
			};
		};
	};

	bpmp {
		i2c {
			vrs@3c {
				compatible = "nvidia,vrs-pseq";
				reg = <0x3c>;
				interrupt-parent = <&pmc>;
				/* VRS Wake ID is 24 */
				interrupts = <24 IRQ_TYPE_LEVEL_LOW>;
				interrupt-controller;
				#interrupt-cells = <2>;
				status = "okay";
			};

			tegra_tmp451: thermal-sensor@4c {
				compatible = "ti,tmp451";
				reg = <0x4c>;
				vcc-supply = <&vdd_1v8_ao>;
				#thermal-sensor-cells = <1>;
				status = "okay";
			};

			vrs11_1@20 {
				compatible = "nvidia,vrs11";
				reg = <0x20>;
				rail-name-loopA = "GPU";
				rail-name-loopB = "CPU";
			};

			vrs11_2@22 {
				compatible = "nvidia,vrs11";
				reg = <0x22>;
				rail-name-loopA = "SOC";
				rail-name-loopB = "CV";
			};
		};
	};

	eeprom-manager {
		bus@0 {
			i2c-bus = <&gen1_i2c>;
			eeprom@0 {
				slave-address = <0x50>;
				label = "cvm";
			};
		};
	};

	reserved-memory {
		linux,cma { /* Needed for nvgpu comptags */
			compatible = "shared-dma-pool";
			reusable;
			size = <0x0 0x10000000>; /* 256MB */
			alignment = <0x0 0x10000>;
			linux,cma-default;
			status = "okay";
		};
	};
};
