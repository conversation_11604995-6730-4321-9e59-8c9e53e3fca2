// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2022-2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

/ {
	bus@0 {
		aon@c000000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					board {
						prod = <
							0 0x00260004 0x0000003f 0x00000020>;            //SPI_COMMAND2_0
					};
				};
			};
		};

		i2c@3160000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_fm {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x003c0000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000202              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x02020202>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
				prod_c_fmplus {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x00160000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000202              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x02020202>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
			};
		};

		i2c@3180000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_fmplus {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x00160000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000202              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x02020202>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
				prod_c_sm {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x004f0000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000708              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x08080808>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
			};
		};

		i2c@3190000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_fm {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x003c0000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000202              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x02020202>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
			};
		};

		i2c@31c0000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_fm {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x003c0000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000202              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x02020202>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
			};
		};

		i2c@31e0000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_fm {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x003c0000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000202              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x02020202>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
			};
		};

		i2c@c240000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_fmplus {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x00160000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000202              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x02020202>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
			};
		};

		i2c@c250000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_fmplus {
					board {
						prod = <
							0 0x0000006c 0xffff0000 0x00160000              //I2C_I2C_CLK_DIVISOR_REGISTER_0
							0 0x00000094 0x0000ffff 0x00000202              //I2C_I2C_INTERFACE_TIMING_0_0
							0 0x00000098 0xffffffff 0x02020202>;            //I2C_I2C_INTERFACE_TIMING_1_0
					};
				};
			};
		};

		mttcan@c310000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_can_2m_1m {
					board {
						prod = <
							0 0x00000048 0x00007f00 0x00000000>;            //M_TTCAN_CORE_TDCR_0
					};
				};
				prod_c_can_5m {
					board {
						prod = <
							0 0x00000048 0x00007f00 0x00000600>;            //M_TTCAN_CORE_TDCR_0
					};
				};
				prod_c_can_8m {
					board {
						prod = <
							0 0x00000048 0x00007f00 0x00000400>;            //M_TTCAN_CORE_TDCR_0
					};
				};
			};
		};

		mttcan@c320000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_can_2m_1m {
					board {
						prod = <
							0 0x00000048 0x00007f00 0x00000000>;            //M_TTCAN_CORE_TDCR_0
					};
				};
				prod_c_can_5m {
					board {
						prod = <
							0 0x00000048 0x00007f00 0x00000600>;            //M_TTCAN_CORE_TDCR_0
					};
				};
				prod_c_can_8m {
					board {
						prod = <
							0 0x00000048 0x00007f00 0x00000400>;            //M_TTCAN_CORE_TDCR_0
					};
				};
			};
		};

		spi@3210000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					board {
						prod = <
							0 0x00000004 0x0000003f 0x00000030>;            //SPI_COMMAND2_0
					};
				};
			};
		};

		spi@3230000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					board {
						prod = <
							0 0x00000004 0x0000003f 0x00000020>;            //SPI_COMMAND2_0
					};
				};
			};
		};

		spi@3270000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					board {
						prod = <
							0 0x000001ec 0x01f1f000 0x00a0a000>;            //QSPI_QSPI_COMP_CONTROL_0
					};
				};
			};
		};

		ufshci@2500000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					board {
						prod = <
							0x02470000 0x00002220 0xffffffff 0x001aadb5            //MPHY_RX_APB_VENDOR3B_0
							0x02480000 0x00002220 0xffffffff 0x001aadb5>;            //MPHY_RX_APB_VENDOR3B_0
					};
				};
			};
		};

		xusb_padctl@3520000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					board {
						prod = <
							0 0x00000088 0x01fe0000 0x00cc0000              //XUSB_PADCTL_USB2_OTG_PAD0_CTL_0_0
							0 0x00000094 0x0000000e 0x00000004              //XUSB_PADCTL_USB2_OTG_PAD0_CTL_3_0
							0 0x000000c8 0x01fe0000 0x00cc0000              //XUSB_PADCTL_USB2_OTG_PAD1_CTL_0_0
							0 0x000000d4 0x0000000e 0x00000004              //XUSB_PADCTL_USB2_OTG_PAD1_CTL_3_0
							0 0x00000108 0x01fe0000 0x00cc0000              //XUSB_PADCTL_USB2_OTG_PAD2_CTL_0_0
							0 0x00000114 0x0000000e 0x00000000              //XUSB_PADCTL_USB2_OTG_PAD2_CTL_3_0
							0 0x00000148 0x01fe0000 0x00cc0000>;            //XUSB_PADCTL_USB2_OTG_PAD3_CTL_0_0
					};
				};
			};
		};
	};
};
