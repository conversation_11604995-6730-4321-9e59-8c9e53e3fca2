// SPDX-License-Identifier: GPL-2.0
#include <dt-bindings/pinctrl/pinctrl-tegra.h>

/ {
	compatible = "nvidia,p3768-0000";

	aliases {
		serial0 = &tcu;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	bus@0 {
		i2c@3160000 {
			status = "okay";

			tca9555_a21: tca9555@21 {
				compatible = "nxp,pca9555";
				reg = <0x21>;
				vcc-supply = <&vdd_1v8_sys>;

				//interrupt-parent = <&gpio_aon>;
				//interrupts = <TEGRA234_AON_GPIO(CC, 2) IRQ_TYPE_LEVEL_LOW>;

				interrupt-controller;
				#interrupt-cells = <2>;

				gpio-controller;
				#gpio-cells = <2>;

				status = "okay";
				gpio-line-names = "CSI0_PWDN", "CSI0_PDB", "CSI0_RST_N", "INFO0_TRIG_IN",
							"CSI1_PWDN", "CSI1_PDB", "CSI1_RST_N", "INFO1_TRIG_IN",
							"", "", "", "",
							"", "", "", "";
			};

			eeprom@57 {
				compatible = "atmel,24c02";
				reg = <0x57>;

				label = "system";
				vcc-supply = <&vdd_1v8_sys>;
				address-width = <8>;
				pagesize = <8>;
				size = <256>;
				read-only;
			};
		};

		i2c@c240000{
			status = "okay";

			pca9555_a23: pca9555@23 {
				compatible = "nxp,pca9555";
				reg = <0x23>;
				vcc-supply = <&hdr40_vdd_3v3>;

				//interrupt-parent = <&gpio_aon>;
				//interrupts = <TEGRA234_AON_GPIO(CC, 0) IRQ_TYPE_LEVEL_LOW>;

				interrupt-controller;
				#interrupt-cells = <2>;

				gpio-controller;
				#gpio-cells = <2>;

				status = "okay";
				gpio-line-names = "M2_DISABLE_N", "M2_RST_N", "LTE_WAKE", "W_DISABLE1_CTRL",
							"BT_M2_EN", "GPIO_LED1", "GPIO_LED4", "GPIO_LED3",
							"BTBC_GPIO_P20", "BTBC_GPIO_P21", "BTBC_GPIO_P22", "BTBC_GPIO_P23",
							"BTBC_GPIO_P24", "BTBP_GPIO_P18", "BTBP_GPIO_P19", "BTBP_GPIO_P20";
			
				KEY-B-enable {
					gpio-hog;
					gpios = <0 GPIO_ACTIVE_HIGH>;
					output-low;
				};
			};

			pca9554abs: pca9554abs@3b {
				compatible = "nxp,pca9554";
				reg = <0x3b>;
				vcc-supply = <&hdr40_vdd_3v3>;

				//interrupt-parent = <&pca9555_a23>;
				//interrupts = <&pca9555_a23 8 IRQ_TYPE_LEVEL_LOW>;

				interrupt-controller;
				#interrupt-cells = <2>;

				gpio-controller;
				#gpio-cells = <2>;

				status = "okay";
				gpio-line-names = "P_GPIO01", "P_GPIO02", "P_GPIO03", "P_GPIO04",
							"P_GPIO05", "P_GPIO06", "P_GPIO07", "P_GPIO08";
			};
		};

		gpio-leds1 {
			compatible = "gpio-leds";
			status = "okay";
			led1 {
				gpios = <&pca9555_a23 5 GPIO_ACTIVE_HIGH>;
				default-state = "off";
			};
		};

		gpio-leds2 {
			compatible = "gpio-leds";
			status = "okay";
			led2 {
				gpios = <&gpio_aon TEGRA234_AON_GPIO(CC, 1)  GPIO_ACTIVE_HIGH>;
				default-state = "on";
			};
		};

		gpio-leds3 {
			compatible = "gpio-leds";
			status = "okay";
			led3 {
				gpios = <&pca9555_a23 7 GPIO_ACTIVE_HIGH>;
				default-state = "off";
			};
		};

		gpio-leds4 {
			compatible = "gpio-leds";
			status = "okay";
			led4 {
				gpios = <&pca9555_a23 6 GPIO_ACTIVE_HIGH>;
				default-state = "off";
			};
		};

		serial@31d0000 {
			current-speed = <115200>;
			status = "okay";
		};

		pwm@32a0000 {
			assigned-clocks = <&bpmp TEGRA234_CLK_PWM3>;
			assigned-clock-parents = <&bpmp TEGRA234_CLK_PLLP_OUT0>;
			status = "okay";
		};

		pwm@32e0000 {/* PWM7 40 pin header, pin 32 */
			status = "okay";
		};

		pinmux@2430000 {
			pwm-pinmux {
				hdr40-pwm5 {
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,function = "gp";
					nvidia,pins = "soc_gpio21_ph0";
				};

				hdr40-pwm7 {
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,function = "gp";
					nvidia,pins = "soc_gpio19_pg6";
				};
			};
		};

		padctl@3520000 {
			status = "okay";

			pads {
				usb2 {
					lanes {
						usb2-0 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb2-1 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb2-2 {
							nvidia,function = "xusb";
							status = "okay";
						};
					};
				};

				usb3 {
					lanes {
						usb3-0 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb3-1 {
							nvidia,function = "xusb";
							status = "okay";
						};
					};
				};
			};

			ports {
				/* recovery port */
				usb2-0 {
					mode = "otg";
					vbus-supply = <&vdd_5v0_sys>;
					status = "okay";
					usb-role-switch;
				};

				/* hub */
				usb2-1 {
					mode = "host";
					vbus-supply = <&vdd_1v1_hub>;
					status = "okay";
				};

				/* M.2 Key-E */
				usb2-2 {
					mode = "host";
					vbus-supply = <&vdd_5v0_sys>;
					status = "okay";
				};

				/* hub */
				usb3-0 {
					nvidia,usb2-companion = <1>;
					status = "okay";
				};

				/* J5 */
				usb3-1 {
					nvidia,usb2-companion = <0>;
					status = "okay";
				};
			};
		};

		usb@3550000 {
			status = "okay";

			phys = <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-1}>;
			phy-names = "usb2-0", "usb3-0";
		};

		usb@3610000 {
			status = "okay";

			phys = <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-1}>,
			       <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-2}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-1}>;
			phy-names = "usb2-0", "usb2-1", "usb2-2", "usb3-0",
				    "usb3-1";
		};

		/* C8 - Ethernet */
		pcie@140a0000 {
			status = "okay";

			num-lanes = <2>;

			phys = <&p2u_gbe_2>, <&p2u_gbe_3>;
			phy-names = "p2u-0", "p2u-1";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;
			vpcie3v3-supply = <&vdd_3v3_pcie>;
		};

		/* C1 - M.2 Key-E */
		pcie@14100000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;

			phys = <&p2u_hsio_3>;
			phy-names = "p2u-0";
		};

		/* C4 - M.2 Key-M */
		pcie@14160000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;

			phys = <&p2u_hsio_4>, <&p2u_hsio_5>, <&p2u_hsio_6>,
			       <&p2u_hsio_7>;
			phy-names = "p2u-0", "p2u-1", "p2u-2", "p2u-3";
		};

		/* C7 - M.2 Key-M */
		pcie@141e0000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;

			phys = <&p2u_gbe_0>, <&p2u_gbe_1>;
			phy-names = "p2u-0", "p2u-1";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-force-recovery {
			label = "Force Recovery";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 0) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <BTN_1>;
		};

		key-power {
			label = "Power";
			gpios = <&gpio_aon TEGRA234_AON_GPIO(EE, 4) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_POWER>;
			wakeup-event-action = <EV_ACT_ASSERTED>;
			wakeup-source;
		};

		key-suspend {
			label = "Suspend";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 2) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_SLEEP>;
		};
	};

	fan: pwm-fan {
		compatible = "pwm-fan";
		pwms = <&pwm3 0 45334>;
		#cooling-cells = <2>;
	};

	reg_can0_stby: regulator-can0-stby {
		compatible = "regulator-fixed";
		regulator-name = "can1-stby";
		pinctrl-names = "default";
		regulator-min-microvolt = <0>;
		regulator-max-microvolt = <0>;
		gpio = <&gpio TEGRA234_MAIN_GPIO(Z, 6) GPIO_ACTIVE_HIGH>;
		enable-active-high;
		status = "okay";
	};

	vdd_1v8_sys: regulator-vdd-1v8-sys {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_SYS";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_1v1_hub: regulator-vdd-1v1-hub {
		compatible = "regulator-fixed";
		regulator-name = "VDD_AV10_HUB";
		regulator-min-microvolt = <1100000>;
		regulator-max-microvolt = <1100000>;
		vin-supply = <&vdd_5v0_sys>;
		regulator-always-on;
	};

	vdd_3v3_pcie: regulator-vdd-3v3-pcie {
		compatible = "regulator-fixed";
		regulator-name = "VDD_3V3_PCIE";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio_aon TEGRA234_AON_GPIO(AA, 5) GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	serial {
		status = "okay";
	};
};
