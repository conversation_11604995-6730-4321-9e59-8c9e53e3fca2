// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

#include <dt-bindings/mailbox/tegra186-hsp.h>
#include <dt-bindings/memory/tegra234-mc.h>
/ {
	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		fsicom_resv: reservation-fsicom {
			iommu-addresses = <&fsicom_client 0x0 0x0 0x0 0xf0000000>,
				<&fsicom_client 0x0 0xf1000000 0xffffffff 0x0effffff>;
		};
		fsicom_resv_inst1: reservation-fsicom_inst1 {
			iommu-addresses = <&fsicom_client_inst1 0x0 0x0 0x0 0xf0000000>,
				<&fsicom_client_inst1 0x0 0xf1000000 0xffffffff 0x0effffff>;
		};
	};

	fsicom_client: fsicom_client {
		compatible = "nvidia,tegra234-fsicom-client";
#if TEGRA_HSP_DT_VERSION >= DT_VERSION_2
		mboxes =
			<&hsp_top2 (TEGRA_HSP_MBOX_TYPE_SM | TEGRA_HSP_MBOX_TYPE_SM_128BIT) TEGRA_HSP_SM_TX(2)>,
			<&hsp_top2 (TEGRA_HSP_MBOX_TYPE_SM | TEGRA_HSP_MBOX_TYPE_SM_128BIT) TEGRA_HSP_SM_RX(1)>,
			<&hsp_top2 (TEGRA_HSP_MBOX_TYPE_SM | TEGRA_HSP_MBOX_TYPE_SM_128BIT) TEGRA_HSP_SM_TX(5)>,
			<&hsp_top2 (TEGRA_HSP_MBOX_TYPE_SM | TEGRA_HSP_MBOX_TYPE_SM_128BIT) TEGRA_HSP_SM_RX(4)>;
#else
		mboxes =
			<&hsp_top2 TEGRA_HSP_MBOX_TYPE_SM_128BIT TEGRA_HSP_SM_TX(2)>,
			<&hsp_top2 TEGRA_HSP_MBOX_TYPE_SM_128BIT TEGRA_HSP_SM_RX(1)>,
			<&hsp_top2 TEGRA_HSP_MBOX_TYPE_SM_128BIT TEGRA_HSP_SM_TX(5)>,
			<&hsp_top2 TEGRA_HSP_MBOX_TYPE_SM_128BIT TEGRA_HSP_SM_RX(4)>;
#endif
		mbox-names = "fsi-tx-cpu0", "fsi-rx-cpu0", "fsi-tx-cpu1", "fsi-rx-cpu1";
		iommus = <&smmu_niso1 TEGRA234_SID_NISO1_FSI_CPU0>;
		memory-region = <&fsicom_resv>;
		dma-coherent;
#if defined(ENABLE_FSI) && !defined(ENABLE_MODS_CONFIG)
		enable-deinit-notify;
#endif
		smmu_inst = <0>;
		max_fsi_core=<1>; /*Value 1 <-> core 0, value 2 <-> core0,1*/
		status = "disabled";
	};

	fsicom_client_inst1: fsicom_client_inst1 {
		compatible = "nvidia,tegra234-fsicom-client";
		iommus = <&smmu_niso1 TEGRA234_SID_NISO1_FSI_CPU1>;
		memory-region = <&fsicom_resv_inst1>;
		dma-coherent;
		smmu_inst = <1>;
		status = "okay";
	};

	safetyservices_epl_client@110000 {
		compatible = "nvidia,tegra234-epl-client";
#if TEGRA_HSP_DT_VERSION >= DT_VERSION_2
		mboxes =
			<&hsp_top2 (TEGRA_HSP_MBOX_TYPE_SM | TEGRA_HSP_MBOX_TYPE_SM_128BIT) TEGRA_HSP_SM_TX(0)>;
#else
		mboxes =
			<&hsp_top2 TEGRA_HSP_MBOX_TYPE_SM_128BIT TEGRA_HSP_SM_TX(0)>;
#endif
		mbox-names = "epl-tx";
		reg = <0x0 0x00110000 0x0 0x4>,
			<0x0 0x00110004 0x0 0x4>,
			<0x0 0x00120000 0x0 0x4>,
			<0x0 0x00120004 0x0 0x4>,
			<0x0 0x00130000 0x0 0x4>,
			<0x0 0x00130004 0x0 0x4>,
			<0x0 0x00140000 0x0 0x4>,
			<0x0 0x00140004 0x0 0x4>,
			<0x0 0x00150000 0x0 0x4>,
			<0x0 0x00150004 0x0 0x4>,
			<0x0 0x024e0038 0x0 0x4>;
		/* Device driver's name for reporting errors via MISCREG_MISC_EC_ERR0_SW_ERR_CODE_0 */
		client-misc-sw-generic-err0 = "fsicom_client";
		/* Device driver's name for reporting errors via MISCREG_MISC_EC_ERR1_SW_ERR_CODE_0 */
		client-misc-sw-generic-err1 = "gk20b";
		/* Device driver's name for reporting errors via MISCREG_MISC_EC_ERR3_SW_ERR_CODE_0 */
		client-misc-sw-generic-err3 = "gk20d";
		/* Device driver's name for reporting errors via MISCREG_MISC_EC_ERR4_SW_ERR_CODE_0 */
		client-misc-sw-generic-err4 = "gk20e";
#if defined(ENABLE_FSI) && !defined(ENABLE_MODS_CONFIG)
		enable-deinit-notify;
#endif
		status = "disabled";
	};

	FsiComIvc {
		compatible = "nvidia,tegra-fsicom-channels";
		status = "disabled";
		nChannel=<7>;
		channel_0{
			frame-count = <4>;
			frame-size = <1024>;
			core-id = <0>;
			NvSciCh = "nvfsicom_EPD";
		};
		channel_1{
			frame-count = <30>;
			frame-size = <64>;
			core-id = <0>;
			NvSciCh = "nvfsicom_CcplexApp";
		};
		channel_2{
			frame-count = <4>;
			frame-size = <64>;
			core-id = <0>;
			NvSciCh = "nvfsicom_CcplexApp_state_change";
		};
		channel_3{
			frame-count = <4>;
			frame-size = <64>;
			core-id = <0>;
			NvSciCh = "nvfsicom_app1";
		};
		channel_4{
			frame-count = <2>;
			frame-size = <64>;
			core-id = <1>;
			NvSciCh = "nvfsicom_app2";
		};
		channel_5{
			frame-count = <4>;
			frame-size = <64>;
			core-id = <0>;
			NvSciCh = "nvfsicom_appGR";
		};
		channel_6{
			frame-count = <4>;
			frame-size = <10240>;
			core-id = <0>;
		};
	};

	FsiComClientChConfigEpd {
		compatible = "nvidia,tegra-fsicom-EPD";
		status = "disabled";
		channelid_list = <0>;
	};
};