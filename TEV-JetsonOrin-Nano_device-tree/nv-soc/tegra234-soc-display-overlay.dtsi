// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2022-2024, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

#include <dt-bindings/power/tegra234-powergate.h>

/ {
	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		fb0_reserved: framebuffer@0,0 {
			compatible = "framebuffer";
			reg = <0x00 0x00 0x00 0x00>;
			iommu-addresses = <&display 0x0 0x0 0x0 0x0>;
			no-map;
			status = "disabled";
		};
	};

	dce@d800000 {
		compatible = "nvidia,tegra234-dce";
		reg = <0x0 0x0d800000 0x0 0x00800000>;
		interrupts =
			<0 376 0x4>,
			<0 377 0x4>;
		interrupt-names = "wdt-remote",
			"dce-sm0";
		iommus = <&smmu_niso0 TEGRA234_SID_DCE>;
		status = "disabled";
	};

	display: display@13800000 {
		compatible = "nvidia,tegra234-display";
		power-domains = <&bpmp TEGRA234_POWER_DOMAIN_DISP>;
		nvidia,num-dpaux-instance = <1>;
		reg-names = "nvdisplay", "dpaux0", "hdacodec", "mipical";
		reg = <0x0 0x13800000 0x0 0xEFFFF    /* nvdisplay */
			0x0 0x155C0000 0x0 0xFFFF     /* dpaux0 */
			0x0 0x0242c000 0x0 0x1000     /* hdacodec */
			0x0 0x03990000 0x0 0x10000>;  /* mipical */
		interrupt-names = "nvdisplay", "dpaux0", "hdacodec";
		interrupts = <0 416 4
			0 419 4
			0  61 4>;
		nvidia,bpmp = <&bpmp>;
		clocks = <&bpmp TEGRA234_CLK_HUB>,
		       <&bpmp TEGRA234_CLK_DISP>,
		       <&bpmp TEGRA234_CLK_NVDISPLAY_P0>,
		       <&bpmp TEGRA234_CLK_NVDISPLAY_P1>,
		       <&bpmp TEGRA234_CLK_DPAUX>,
		       <&bpmp TEGRA234_CLK_FUSE>,
		       <&bpmp TEGRA234_CLK_DSIPLL_VCO>,
		       <&bpmp TEGRA234_CLK_DSIPLL_CLKOUTPN>,
		       <&bpmp TEGRA234_CLK_DSIPLL_CLKOUTA>,
		       <&bpmp TEGRA234_CLK_SPPLL0_VCO>,
		       <&bpmp TEGRA234_CLK_SPPLL0_CLKOUTPN>,
		       <&bpmp TEGRA234_CLK_SPPLL0_CLKOUTA>,
		       <&bpmp TEGRA234_CLK_SPPLL0_CLKOUTB>,
		       <&bpmp TEGRA234_CLK_SPPLL0_DIV10>,
		       <&bpmp TEGRA234_CLK_SPPLL0_DIV25>,
		       <&bpmp TEGRA234_CLK_SPPLL0_DIV27PN>,
		       <&bpmp TEGRA234_CLK_SPPLL1_VCO>,
		       <&bpmp TEGRA234_CLK_SPPLL1_CLKOUTPN>,
		       <&bpmp TEGRA234_CLK_SPPLL1_DIV27PN>,
		       <&bpmp TEGRA234_CLK_VPLL0_REF>,
		       <&bpmp TEGRA234_CLK_VPLL0>,
		       <&bpmp TEGRA234_CLK_VPLL1>,
		       <&bpmp TEGRA234_CLK_NVDISPLAY_P0_REF>,
		       <&bpmp TEGRA234_CLK_RG0>,
		       <&bpmp TEGRA234_CLK_RG1>,
		       <&bpmp TEGRA234_CLK_DISPPLL>,
		       <&bpmp TEGRA234_CLK_DISPHUBPLL>,
		       <&bpmp TEGRA234_CLK_DSI_LP>,
		       <&bpmp TEGRA234_CLK_DSI_CORE>,
		       <&bpmp TEGRA234_CLK_DSI_PIXEL>,
		       <&bpmp TEGRA234_CLK_PRE_SOR0>,
		       <&bpmp TEGRA234_CLK_PRE_SOR1>,
		       <&bpmp TEGRA234_CLK_DP_LINK_REF>,
		       <&bpmp TEGRA234_CLK_SOR_LINKA_INPUT>,
		       <&bpmp TEGRA234_CLK_SOR_LINKA_AFIFO>,
		       <&bpmp TEGRA234_CLK_SOR_LINKA_AFIFO_M>,
		       <&bpmp TEGRA234_CLK_RG0_M>,
		       <&bpmp TEGRA234_CLK_RG1_M>,
		       <&bpmp TEGRA234_CLK_SOR0_M>,
		       <&bpmp TEGRA234_CLK_SOR1_M>,
		       <&bpmp TEGRA234_CLK_PLLHUB>,
		       <&bpmp TEGRA234_CLK_SOR0>,
		       <&bpmp TEGRA234_CLK_SOR1>,
		       <&bpmp TEGRA234_CLK_SOR_PAD_INPUT>,
		       <&bpmp TEGRA234_CLK_PRE_SF0>,
		       <&bpmp TEGRA234_CLK_SF0>,
		       <&bpmp TEGRA234_CLK_SF1>,
		       <&bpmp TEGRA234_CLK_DSI_PAD_INPUT>,
		       <&bpmp TEGRA234_CLK_PRE_SOR0_REF>,
		       <&bpmp TEGRA234_CLK_PRE_SOR1_REF>,
		       <&bpmp TEGRA234_CLK_SOR0_PLL_REF>,
		       <&bpmp TEGRA234_CLK_SOR1_PLL_REF>,
		       <&bpmp TEGRA234_CLK_SOR0_REF>,
		       <&bpmp TEGRA234_CLK_SOR1_REF>,
		       <&bpmp TEGRA234_CLK_OSC>,
		       <&bpmp TEGRA234_CLK_DSC>,
		       <&bpmp TEGRA234_CLK_MAUD>,
		       <&bpmp TEGRA234_CLK_AZA_2XBIT>,
		       <&bpmp TEGRA234_CLK_AZA_BIT>,
		       <&bpmp TEGRA234_CLK_MIPI_CAL>,
		       <&bpmp TEGRA234_CLK_UART_FST_MIPI_CAL>,
		       <&bpmp TEGRA234_CLK_SOR0_DIV>;
		clock-names = "nvdisplayhub_clk",
			"nvdisplay_disp_clk",
			"nvdisplay_p0_clk",
			"nvdisplay_p1_clk",
			"dpaux0_clk",
			"fuse_clk",
			"dsipll_vco_clk",
			"dsipll_clkoutpn_clk",
			"dsipll_clkouta_clk",
			"sppll0_vco_clk",
			"sppll0_clkoutpn_clk",
			"sppll0_clkouta_clk",
			"sppll0_clkoutb_clk",
			"sppll0_div10_clk",
			"sppll0_div25_clk",
			"sppll0_div27_clk",
			"sppll1_vco_clk",
			"sppll1_clkoutpn_clk",
			"sppll1_div27_clk",
			"vpll0_ref_clk",
			"vpll0_clk",
			"vpll1_clk",
			"nvdisplay_p0_ref_clk",
			"rg0_clk",
			"rg1_clk",
			"disppll_clk",
			"disphubpll_clk",
			"dsi_lp_clk",
			"dsi_core_clk",
			"dsi_pixel_clk",
			"pre_sor0_clk",
			"pre_sor1_clk",
			"dp_link_ref_clk",
			"sor_linka_input_clk",
			"sor_linka_afifo_clk",
			"sor_linka_afifo_m_clk",
			"rg0_m_clk",
			"rg1_m_clk",
			"sor0_m_clk",
			"sor1_m_clk",
			"pllhub_clk",
			"sor0_clk",
			"sor1_clk",
			"sor_pad_input_clk",
			"pre_sf0_clk",
			"sf0_clk",
			"sf1_clk",
			"dsi_pad_input_clk",
			"pre_sor0_ref_clk",
			"pre_sor1_ref_clk",
			"sor0_ref_pll_clk",
			"sor1_ref_pll_clk",
			"sor0_ref_clk",
			"sor1_ref_clk",
			"osc_clk",
			"dsc_clk",
			"maud_clk",
			"aza_2xbit_clk",
			"aza_bit_clk",
			"mipi_cal_clk",
			"uart_fst_mipi_cal_clk",
			"sor0_div_clk";
		resets = <&bpmp TEGRA234_RESET_NVDISPLAY>,
		       <&bpmp TEGRA234_RESET_DPAUX>,
		       <&bpmp TEGRA234_RESET_DSI_CORE>,
		       <&bpmp TEGRA234_RESET_MIPI_CAL>;
		reset-names = "nvdisplay_reset",
			"dpaux0_reset",
			"dsi_core_reset",
			"mipi_cal_reset";
		hdcp_enabled;
		status = "disabled";
		memory-region = <&fb0_reserved>;
		nvidia,disp-sw-soc-chip-id = <0x2350>;
#if TEGRA_IOMMU_DT_VERSION >= DT_VERSION_2
		interconnects = <&mc TEGRA234_MEMORY_CLIENT_NVDISPLAYR &emc>,
			      <&mc TEGRA234_MEMORY_CLIENT_NVDISPLAYR1 &emc>;
		interconnect-names = "dma-mem", "read-1";
#endif
		iommus = <&smmu_iso TEGRA234_SID_ISO_NVDISPLAY>;
		non-coherent;
		nvdisplay-niso {
			compatible = "nvidia,tegra234-display-niso";
			iommus = <&smmu_niso0 TEGRA234_SID_NVDISPLAY>;
			dma-coherent;
		};
	};
};
