// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2022-2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

/ {
	bus@0 {
		i2c@3160000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x0000009c 0x0000ffff 0x00000308              //i2c_i2c_hs_interface_timing_0_0
						0 0x000000d4 0x000000ff 0x00000000              //i2c_i2c_interface_timing_2_0
						0 0x000000d8 0x000000ff 0x00000000              //i2c_i2c_hs_interface_timing_2_0
						0 0x000000dc 0x0000ffff 0x00000001              //i2c_i2c_mstr_data_capture_timing_0
						0 0x000000e0 0x0000ffff 0x00000002>;            //i2c_i2c_slv_data_capture_timing_0
				};
				prod_c_fm {
					prod = <
						0 0x0000006c 0xffff0000 0x003c0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_fmplus {
					prod = <
						0 0x0000006c 0xffff0000 0x00160000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_hs {
					prod = <
						0 0x0000006c 0xffffffff 0x00160002              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202              //i2c_i2c_interface_timing_1_0
						0 0x000000a0 0x00ffffff 0x00090909>;            //i2c_i2c_hs_interface_timing_1_0
				};
				prod_c_sm {
					prod = <
						0 0x0000006c 0xffff0000 0x004f0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000708              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x08080808>;            //i2c_i2c_interface_timing_1_0
				};
			};
		};

		i2c@3180000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x0000009c 0x0000ffff 0x00000308              //i2c_i2c_hs_interface_timing_0_0
						0 0x000000d4 0x000000ff 0x00000000              //i2c_i2c_interface_timing_2_0
						0 0x000000d8 0x000000ff 0x00000000              //i2c_i2c_hs_interface_timing_2_0
						0 0x000000dc 0x0000ffff 0x00000001              //i2c_i2c_mstr_data_capture_timing_0
						0 0x000000e0 0x0000ffff 0x00000002>;            //i2c_i2c_slv_data_capture_timing_0
				};
				prod_c_fm {
					prod = <
						0 0x0000006c 0xffff0000 0x003c0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_fmplus {
					prod = <
						0 0x0000006c 0xffff0000 0x00160000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_hs {
					prod = <
						0 0x0000006c 0xffffffff 0x00160002              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202              //i2c_i2c_interface_timing_1_0
						0 0x000000a0 0x00ffffff 0x00090909>;            //i2c_i2c_hs_interface_timing_1_0
				};
				prod_c_sm {
					prod = <
						0 0x0000006c 0xffff0000 0x004f0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000708              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x08080808>;            //i2c_i2c_interface_timing_1_0
				};
			};
		};

		i2c@3190000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x0000009c 0x0000ffff 0x00000308              //i2c_i2c_hs_interface_timing_0_0
						0 0x000000d4 0x000000ff 0x00000000              //i2c_i2c_interface_timing_2_0
						0 0x000000d8 0x000000ff 0x00000000              //i2c_i2c_hs_interface_timing_2_0
						0 0x000000dc 0x0000ffff 0x00000001              //i2c_i2c_mstr_data_capture_timing_0
						0 0x000000e0 0x0000ffff 0x00000002>;            //i2c_i2c_slv_data_capture_timing_0
				};
				prod_c_fm {
					prod = <
						0 0x0000006c 0xffff0000 0x003c0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_fmplus {
					prod = <
						0 0x0000006c 0xffff0000 0x00160000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_hs {
					prod = <
						0 0x0000006c 0xffffffff 0x00160002              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202              //i2c_i2c_interface_timing_1_0
						0 0x000000a0 0x00ffffff 0x00090909>;            //i2c_i2c_hs_interface_timing_1_0
				};
				prod_c_sm {
					prod = <
						0 0x0000006c 0xffff0000 0x004f0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000708              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x08080808>;            //i2c_i2c_interface_timing_1_0
				};
			};
		};

		i2c@31b0000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x0000009c 0x0000ffff 0x00000308              //i2c_i2c_hs_interface_timing_0_0
						0 0x000000d4 0x000000ff 0x00000000              //i2c_i2c_interface_timing_2_0
						0 0x000000d8 0x000000ff 0x00000000              //i2c_i2c_hs_interface_timing_2_0
						0 0x000000dc 0x0000ffff 0x00000001              //i2c_i2c_mstr_data_capture_timing_0
						0 0x000000e0 0x0000ffff 0x00000002>;            //i2c_i2c_slv_data_capture_timing_0
				};
				prod_c_fm {
					prod = <
						0 0x0000006c 0xffff0000 0x003c0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_fmplus {
					prod = <
						0 0x0000006c 0xffff0000 0x00160000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_hs {
					prod = <
						0 0x0000006c 0xffffffff 0x00160002              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202              //i2c_i2c_interface_timing_1_0
						0 0x000000a0 0x00ffffff 0x00090909>;            //i2c_i2c_hs_interface_timing_1_0
				};
				prod_c_sm {
					prod = <
						0 0x0000006c 0xffff0000 0x004f0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000708              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x08080808>;            //i2c_i2c_interface_timing_1_0
				};
			};
		};

		i2c@31c0000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x0000009c 0x0000ffff 0x00000308              //i2c_i2c_hs_interface_timing_0_0
						0 0x000000d4 0x000000ff 0x00000000              //i2c_i2c_interface_timing_2_0
						0 0x000000d8 0x000000ff 0x00000000              //i2c_i2c_hs_interface_timing_2_0
						0 0x000000dc 0x0000ffff 0x00000001              //i2c_i2c_mstr_data_capture_timing_0
						0 0x000000e0 0x0000ffff 0x00000002>;            //i2c_i2c_slv_data_capture_timing_0
				};
				prod_c_fm {
					prod = <
						0 0x0000006c 0xffff0000 0x003c0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_fmplus {
					prod = <
						0 0x0000006c 0xffff0000 0x00160000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_hs {
					prod = <
						0 0x0000006c 0xffffffff 0x00160002              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202              //i2c_i2c_interface_timing_1_0
						0 0x000000a0 0x00ffffff 0x00090909>;            //i2c_i2c_hs_interface_timing_1_0
				};
				prod_c_sm {
					prod = <
						0 0x0000006c 0xffff0000 0x004f0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000708              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x08080808>;            //i2c_i2c_interface_timing_1_0
				};
			};
		};

		i2c@31e0000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x0000009c 0x0000ffff 0x00000308              //i2c_i2c_hs_interface_timing_0_0
						0 0x000000d4 0x000000ff 0x00000000              //i2c_i2c_interface_timing_2_0
						0 0x000000d8 0x000000ff 0x00000000              //i2c_i2c_hs_interface_timing_2_0
						0 0x000000dc 0x0000ffff 0x00000001              //i2c_i2c_mstr_data_capture_timing_0
						0 0x000000e0 0x0000ffff 0x00000002>;            //i2c_i2c_slv_data_capture_timing_0
				};
				prod_c_fm {
					prod = <
						0 0x0000006c 0xffff0000 0x003c0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_fmplus {
					prod = <
						0 0x0000006c 0xffff0000 0x00160000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_hs {
					prod = <
						0 0x0000006c 0xffffffff 0x00160002              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202              //i2c_i2c_interface_timing_1_0
						0 0x000000a0 0x00ffffff 0x00090909>;            //i2c_i2c_hs_interface_timing_1_0
				};
				prod_c_sm {
					prod = <
						0 0x0000006c 0xffff0000 0x004f0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000708              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x08080808>;            //i2c_i2c_interface_timing_1_0
				};
			};
		};

		i2c@c240000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x0000009c 0x0000ffff 0x00000308              //i2c_i2c_hs_interface_timing_0_0
						0 0x000000d4 0x000000ff 0x00000000              //i2c_i2c_interface_timing_2_0
						0 0x000000d8 0x000000ff 0x00000000              //i2c_i2c_hs_interface_timing_2_0
						0 0x000000dc 0x0000ffff 0x00000001              //i2c_i2c_mstr_data_capture_timing_0
						0 0x000000e0 0x0000ffff 0x00000002>;            //i2c_i2c_slv_data_capture_timing_0
				};
				prod_c_fm {
					prod = <
						0 0x0000006c 0xffff0000 0x003c0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_fmplus {
					prod = <
						0 0x0000006c 0xffff0000 0x00160000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_hs {
					prod = <
						0 0x0000006c 0xffffffff 0x00160002              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202              //i2c_i2c_interface_timing_1_0
						0 0x000000a0 0x00ffffff 0x00090909>;            //i2c_i2c_hs_interface_timing_1_0
				};
				prod_c_sm {
					prod = <
						0 0x0000006c 0xffff0000 0x004f0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000708              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x08080808>;            //i2c_i2c_interface_timing_1_0
				};
			};
		};

		i2c@c250000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x0000009c 0x0000ffff 0x00000308              //i2c_i2c_hs_interface_timing_0_0
						0 0x000000d4 0x000000ff 0x00000000              //i2c_i2c_interface_timing_2_0
						0 0x000000d8 0x000000ff 0x00000000              //i2c_i2c_hs_interface_timing_2_0
						0 0x000000dc 0x0000ffff 0x00000001              //i2c_i2c_mstr_data_capture_timing_0
						0 0x000000e0 0x0000ffff 0x00000002>;            //i2c_i2c_slv_data_capture_timing_0
				};
				prod_c_fm {
					prod = <
						0 0x0000006c 0xffff0000 0x003c0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_fmplus {
					prod = <
						0 0x0000006c 0xffff0000 0x00160000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202>;            //i2c_i2c_interface_timing_1_0
				};
				prod_c_hs {
					prod = <
						0 0x0000006c 0xffffffff 0x00160002              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000202              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x02020202              //i2c_i2c_interface_timing_1_0
						0 0x000000a0 0x00ffffff 0x00090909>;            //i2c_i2c_hs_interface_timing_1_0
				};
				prod_c_sm {
					prod = <
						0 0x0000006c 0xffff0000 0x004f0000              //i2c_i2c_clk_divisor_register_0
						0 0x00000094 0x0000ffff 0x00000708              //i2c_i2c_interface_timing_0_0
						0 0x00000098 0xffffffff 0x08080808>;            //i2c_i2c_interface_timing_1_0
				};
			};
		};

		mmc@3400000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_1_8v {
					prod = <
						0 0x000001e0 0x01f00000 0x00800000>;            //SDMMCA_SDMEMCOMPPADCTRL_0
				};
				prod_c_3_3v {
					prod = <
						0 0x000001e0 0x01f00000 0x00900000>;            //SDMMCA_SDMEMCOMPPADCTRL_0
				};
				prod {
					prod = <
						0 0x00000028 0x00000022 0x00000002              //SDMMCA_POWER_CONTROL_HOST_0
						0 0x00000100 0x1fff006a 0x0e080020              //SDMMCA_VENDOR_CLOCK_CNTRL_0
						0 0x00000128 0x42000000 0x00000000              //SDMMCA_VENDOR_MISC_CNTRL2_0
						0 0x000001c0 0x00001fc0 0x00000040              //SDMMCA_VENDOR_TUNING_CNTRL0_0
						0 0x000001e0 0x0001f000 0x00009000              //SDMMCA_SDMEMCOMPPADCTRL_0
						0 0x000001e4 0x20000000 0x20000000>;            //SDMMCA_AUTO_CAL_CONFIG_0
				};
				prod_c_ddr50 {
					prod = <
						0 0x0000003c 0x00070000 0x00040000>;            //SDMMCA_AUTO_CMD12_ERR_STATUS_0
				};
				prod_c_ddr52 {
					prod = <
						0 0x0000003c 0x00070000 0x00040000>;            //SDMMCA_AUTO_CMD12_ERR_STATUS_0
				};
				prod_c_hs200 {
					prod = <
						0 0x0000003c 0x00070000 0x00030000              //SDMMCA_AUTO_CMD12_ERR_STATUS_0
						0 0x000001c0 0x0000e000 0x00004000>;            //SDMMCA_VENDOR_TUNING_CNTRL0_0
				};
				prod_c_nopwrsave {
					prod = <
						0 0x00000100 0x00000001 0x00000001              //SDMMCA_VENDOR_CLOCK_CNTRL_0
						0 0x000001ac 0x00000004 0x00000000>;            //SDMMCA_VENDOR_IO_TRIM_CNTRL_0
				};
				prod_c_pwrsave {
					prod = <
						0 0x00000100 0x00000001 0x00000000              //SDMMCA_VENDOR_CLOCK_CNTRL_0
						0 0x000001ac 0x00000004 0x00000004>;            //SDMMCA_VENDOR_IO_TRIM_CNTRL_0
				};
				prod_c_sdr104 {
					prod = <
						0 0x0000003c 0x00070000 0x00030000              //SDMMCA_AUTO_CMD12_ERR_STATUS_0
						0 0x000001c0 0x0000e000 0x00004000>;            //SDMMCA_VENDOR_TUNING_CNTRL0_0
				};
				prod_c_sdr12 {
					prod = <
						0 0x0000003c 0x00070000 0x00000000>;            //SDMMCA_AUTO_CMD12_ERR_STATUS_0
				};
				prod_c_sdr25 {
					prod = <
						0 0x0000003c 0x00070000 0x00010000>;            //SDMMCA_AUTO_CMD12_ERR_STATUS_0
				};
				prod_c_sdr50 {
					prod = <
						0 0x0000003c 0x00070000 0x00020000              //SDMMCA_AUTO_CMD12_ERR_STATUS_0
						0 0x000001c0 0x0000e000 0x00008000>;            //SDMMCA_VENDOR_TUNING_CNTRL0_0
				};
			};
		};
		mmc@3460000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x00000004 0x00000fff 0x00000200              //sdmmcab_block_size_block_count_0
						0 0x00000028 0x00000020 0x00000020              //sdmmcab_power_control_host_0
						0 0x00000100 0x1f00006a 0x12000020              //sdmmcab_vendor_clock_cntrl_0
						0 0x00000128 0x43000000 0x00000000              //sdmmcab_vendor_misc_cntrl2_0
						0 0x000001c0 0x00001fc0 0x00000040              //sdmmcab_vendor_tuning_cntrl0_0
						0 0x000001e0 0x01f1f000 0x00a0a000              //sdmmcab_sdmemcomppadctrl_0
						0 0x000001e4 0x20000000 0x20000000>;            //sdmmcab_auto_cal_config_0
				};
				prod_c_ddr50 {
					prod = <
						0 0x0000003c 0x00070000 0x00040000              //sdmmcab_auto_cmd12_err_status_0
						0 0x00000100 0x1fff0000 0x12070000>;            //sdmmcab_vendor_clock_cntrl_0
				};
				prod_c_ddr52 {
					prod = <
						0 0x0000003c 0x00070000 0x00040000              //sdmmcab_auto_cmd12_err_status_0
						0 0x00000100 0x1fff0000 0x12070000>;            //sdmmcab_vendor_clock_cntrl_0
				};
				prod_c_hs200 {
					prod = <
						0 0x0000003c 0x00070000 0x00030000              //sdmmcab_auto_cmd12_err_status_0
						0 0x000001c0 0x0000e000 0x00004000>;            //sdmmcab_vendor_tuning_cntrl0_0
				};
				prod_c_hs400 {
					prod = <
						0 0x0000003c 0x00070000 0x00050000              //sdmmcab_auto_cmd12_err_status_0
						0 0x00000100 0x00000008 0x00000008              //sdmmcab_vendor_clock_cntrl_0
						0 0x0000010c 0x00003f00 0x00002800              //sdmmcab_vendor_cap_overrides_0
						0 0x000001c0 0x0000e000 0x00004000>;            //sdmmcab_vendor_tuning_cntrl0_0
				};
				prod_c_nopwrsave {
					prod = <
						0 0x00000100 0x00000001 0x00000001              //sdmmcab_vendor_clock_cntrl_0
						0 0x000001ac 0x00000004 0x00000000>;            //sdmmcab_vendor_io_trim_cntrl_0
				};
				prod_c_pwrsave {
					prod = <
						0 0x00000100 0x00000001 0x00000000              //sdmmcab_vendor_clock_cntrl_0
						0 0x000001ac 0x00000004 0x00000004>;            //sdmmcab_vendor_io_trim_cntrl_0
				};
				prod_c_sdr12 {
					prod = <
						0 0x00000100 0x1fff0000 0x12070000>;            //sdmmcab_vendor_clock_cntrl_0
				};
				prod_c_sdr25 {
					prod = <
						0 0x00000100 0x1fff0000 0x12070000>;            //sdmmcab_vendor_clock_cntrl_0
				};
				prod_c_sdr50 {
					prod = <
						0 0x0000003c 0x00070000 0x00020000>;            //sdmmcab_auto_cmd12_err_status_0
				};
			};
		};

		spi@3210000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x00000194 0x80000000 0x00000000>;            //spi_misc_0
				};
			};
		};

		spi@3230000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x00000194 0x80000000 0x00000000>;            //spi_misc_0
				};
			};
		};

		spi@3240000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x00000194 0x80000000 0x00000000>;            //spi_misc_0
				};
			};
		};

		spi@3250000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x00000194 0x80000000 0x00000000>;            //spi_misc_0
				};
			};
		};

		spi@3270000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_nonsecure {
					prod = <
						0 0x0000f000 0x0000003f 0x00000012>;            //qspi_secure_axi_ctl_0
				};
				prod_c_nopwrsave {
					prod = <
						0 0x00000194 0x80000000 0x80000000              //qspi_misc_0
						0 0x000001ec 0x00000002 0x00000000              //qspi_qspi_comp_control_0
						0 0x000001fc 0x00000002 0x00000000>;            //qspi_io_trim_cntrl_0
				};
				prod_c_pwrsave {
					prod = <
						0 0x00000194 0x80000000 0x00000000              //qspi_misc_0
						0 0x000001ec 0x00000002 0x00000002              //qspi_qspi_comp_control_0
						0 0x000001fc 0x00000002 0x00000002>;            //qspi_io_trim_cntrl_0
				};
				prod_c_secure {
					prod = <
						0 0x0000f000 0x0000003f 0x00000000>;            //qspi_secure_axi_ctl_0
				};
			};
		};

		spi@3300000 {
			prod-settings {
				#prod-cells = <4>;
				prod_c_nonsecure {
					prod = <
						0 0x0000f000 0x0000003f 0x00000012>;            //qspi_secure_axi_ctl_0
				};
				prod_c_nopwrsave {
					prod = <
						0 0x00000194 0x80000000 0x80000000              //qspi_misc_0
						0 0x000001ec 0x00000002 0x00000000              //qspi_qspi_comp_control_0
						0 0x000001fc 0x00000002 0x00000000>;            //qspi_io_trim_cntrl_0
				};
				prod_c_pwrsave {
					prod = <
						0 0x00000194 0x80000000 0x00000000              //qspi_misc_0
						0 0x000001ec 0x00000002 0x00000002              //qspi_qspi_comp_control_0
						0 0x000001fc 0x00000002 0x00000002>;            //qspi_io_trim_cntrl_0
				};
				prod_c_secure {
					prod = <
						0 0x0000f000 0x0000003f 0x00000000>;            //qspi_secure_axi_ctl_0
				};
			};
		};

		spi@c260000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x00000194 0x80000000 0x00000000>;            //spi_misc_0
				};
			};
		};

		xusb_padctl@3520000 {
			prod-settings {
				#prod-cells = <4>;
				prod {
					prod = <
						0 0x00000284 0x00000038 0x00000038              //XUSB_PADCTL_USB2_BIAS_PAD_CTL_0_0
						0 0x00000288 0x03fff000 0x0051e000>;            //XUSB_PADCTL_USB2_BIAS_PAD_CTL_1_0
				};
			};
		};
	};
};
