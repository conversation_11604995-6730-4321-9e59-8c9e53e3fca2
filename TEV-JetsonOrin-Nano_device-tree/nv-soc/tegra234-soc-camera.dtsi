// SPDX-License-Identifier: GPL-2.0-only
// SPDX-FileCopyrightText: Copyright (c) 2022-2024, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.

/*
 * tegra234-soc-camera.dtsi: Camera RTCPU DTSI file.
 */


#include <dt-bindings/mailbox/tegra186-hsp.h>
#include <dt-bindings/interrupt/tegra234-irq.h>
#include <dt-bindings/power/tegra234-powergate.h>
#include <dt-bindings/memory/tegra234-mc.h>

/ {
	aliases { /* RCE is the Camera RTCPU */
		tegra-camera-rtcpu = "/rtcpu@bc00000";
	};

	bus@0 {
		host1x@13e00000 {
			vi0: vi0@15c00000 {
				compatible = "nvidia,tegra234-vi";
				clocks = <&bpmp TEGRA234_CLK_VI>;
				clock-names = "vi";
				nvidia,vi-falcon-device = <&vi0_thi>;
				resets = <&bpmp TEGRA234_RESET_VI>;
				reset-names = "vi0";
				iommus = <&smmu_iso TEGRA234_SID_ISO_VI>;
				interconnects = <&mc TEGRA234_MEMORY_CLIENT_VIW &emc>;
				interconnect-names = "write";
				dma-noncoherent;
				status = "okay";
			};

			vi0_thi: vi0-thi@15f00000 {
				compatible = "nvidia,tegra234-vi-thi";
				resets = <&bpmp TEGRA234_RESET_VI>;
				reset-names = "vi0_thi";
				iommus = <&smmu_iso TEGRA234_SID_ISO_VI>;
				dma-noncoherent;
				interconnects = <&mc TEGRA234_MEMORY_CLIENT_VI2FALR &emc>,
						<&mc TEGRA234_MEMORY_CLIENT_VI2FALW &emc>;
				interconnect-names = "dma-mem", "write";
				status = "okay";
			};

			vi1: vi1@14c00000 {
				compatible = "nvidia,tegra234-vi";
				clocks = <&bpmp TEGRA234_CLK_VI>;
				clock-names = "vi";
				nvidia,vi-falcon-device = <&vi1_thi>;
				resets = <&bpmp TEGRA234_RESET_VI2>;
				reset-names = "vi1";
				iommus = <&smmu_iso TEGRA234_SID_ISO_VI2>;
				interconnects = <&mc TEGRA234_MEMORY_CLIENT_VI2W &emc>;
				interconnect-names = "write";
				dma-noncoherent;
				status = "okay";
			};

			vi1_thi: vi1-thi@14f00000 {
				compatible = "nvidia,tegra234-vi-thi";
				resets = <&bpmp TEGRA234_RESET_VI2>;
				reset-names = "vi1_thi";
				iommus = <&smmu_iso TEGRA234_SID_ISO_VI2>;
				dma-noncoherent;
				interconnects = <&mc TEGRA234_MEMORY_CLIENT_VIFALR &emc>,
						<&mc TEGRA234_MEMORY_CLIENT_VIFALW &emc>;
				interconnect-names = "dma-mem", "write";
				status = "okay";
			};

			isp: isp@14800000 {
				compatible = "nvidia,tegra194-isp";
				reg = <0x0 0x14800000 0x0 0x00010000>;

				resets = <&bpmp TEGRA234_RESET_ISP>;
				reset-names = "isp";
				clocks = <&bpmp TEGRA234_CLK_ISP>;
				clock-names = "isp";
				power-domains = <&bpmp TEGRA234_POWER_DOMAIN_ISPA>;
				nvidia,isp-falcon-device = <&isp_thi>;

				iommus = <&smmu_niso1 TEGRA234_SID_ISP>;
				dma-coherent;
				status = "okay";
			};

			isp_thi: isp-thi@14b00000 {
				compatible = "nvidia,tegra194-isp-thi";
				resets = <&bpmp TEGRA234_RESET_ISP>;
				iommus = <&smmu_niso1 TEGRA234_SID_ISP>;
				dma-coherent;
				status = "okay";
			};

			nvcsi: nvcsi@15a00000 {
				compatible = "nvidia,tegra194-nvcsi";
				resets = <&bpmp TEGRA234_RESET_NVCSI>;
				reset-names = "nvcsi";
				clocks = <&bpmp TEGRA234_CLK_NVCSI>;
				clock-names = "nvcsi";
				status = "okay";
			};
		};
	};

	tegra_rce: rtcpu@bc00000 {
		compatible = "nvidia,tegra194-rce";

		nvidia,cpu-name = "rce";

		reg =	<0 0xbc00000 0 0x1000>,   /* RCE EVP (RCE_ATCM_EVP) */
			<0 0xb9f0000 0 0x40000>,  /* RCE PM */
			<0 0xb840000 0 0x10000>,
			<0 0xb850000 0 0x10000>;

		reg-names = "rce-evp", "rce-pm",
				"ast-cpu", "ast-dma";

		clocks =
			<&bpmp TEGRA234_CLK_RCE_CPU_NIC>,
			<&bpmp TEGRA234_CLK_RCE_NIC>,
			<&bpmp TEGRA234_CLK_RCE_CPU>;
		clock-names = "rce-cpu-nic", "rce-nic", "rce-cpu";

		nvidia,clock-rates =
			<115200000 601600000>,
			<115200000 601600000>,
			<115200000 601600000>;

		resets = <&bpmp TEGRA234_RESET_RCE_ALL>;
		reset-names = "rce-all";

		interrupts = <GIC_SPI TEGRA234_IRQ_RCE_WDT_REMOTE IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "wdt-remote";

		iommus = <&smmu_niso0 TEGRA234_SID_RCE>;
		memory-region = <&rce_resv>;
		dma-coherent;

		/* Memory bandwidth in kB/s during boot */
		nvidia,test-bw = <2400000>;

		nvidia,trace = <&rtcpu_trace 4 0x70100000 0x100000>;
		nvidia,ivc-channels = <&camera_ivc_channels 2 0x90000000 0x10000>;

		interconnects = <&mc TEGRA234_MEMORY_CLIENT_RCER &emc>,
				<&mc TEGRA234_MEMORY_CLIENT_RCEW &emc>;
		interconnect-names = "dma-mem", "write";

		nvidia,autosuspend-delay-ms = <5000>;
		status = "okay";

		hsp-vm1 {
			compatible = "nvidia,tegra-camrtc-hsp-vm";
			mboxes =
				<&hsp_rce TEGRA_HSP_MBOX_TYPE_SM TEGRA_HSP_SM_TX(0)>,
				<&hsp_rce TEGRA_HSP_MBOX_TYPE_SM TEGRA_HSP_SM_RX(1)>,
				<&hsp_rce TEGRA_HSP_MBOX_TYPE_SS 0>;
			mbox-names = "vm-tx", "vm-rx", "vm-ss";
			status = "okay";
		};

		hsp-vm2 {
			compatible = "nvidia,tegra-camrtc-hsp-vm";
			mboxes =
				<&hsp_rce TEGRA_HSP_MBOX_TYPE_SM TEGRA_HSP_SM_TX(2)>,
				<&hsp_rce TEGRA_HSP_MBOX_TYPE_SM TEGRA_HSP_SM_RX(3)>,
				<&hsp_rce TEGRA_HSP_MBOX_TYPE_SS 1>;
			mbox-names = "vm-tx", "vm-rx", "vm-ss";
			status = "disabled";
		};
	};

	camera_ivc_channels: camera-ivc-channels {
		echo@0 {
			compatible = "nvidia,tegra186-camera-ivc-protocol-echo";
			nvidia,service = "echo";
			nvidia,version = <0>;
			nvidia,group = <1>;
			nvidia,frame-count = <16>;
			nvidia,frame-size = <64>;
		};
		dbg@1 {
			/* This is raw channel exposed as device */
			compatible = "nvidia,tegra186-camera-ivc-protocol-dbg";
			nvidia,service = "debug";
			nvidia,version = <0>;
			nvidia,group = <1>;
			nvidia,frame-count = <1>;
			nvidia,frame-size = <512>;
		};
		dbg@2 {
			/* This is exposed in debugfs */
			compatible = "nvidia,tegra186-camera-ivc-protocol-debug";
			nvidia,service = "debug";
			nvidia,version = <0>;
			nvidia,group = <1>;
			nvidia,frame-count = <1>;
			nvidia,frame-size = <8192>;
			nvidia,ivc-timeout = <50>;
			nvidia,test-timeout = <5000>;
			nvidia,mem-map = <&tegra_rce &vi0 &isp &vi1>;
			/* Memory bandwidth in kB/s during tests */
			nvidia,test-bw = <2400000>;
		};
		ivccontrol@3 {
			compatible = "nvidia,tegra186-camera-ivc-protocol-capture-control";
			nvidia,service = "capture-control";
			nvidia,version = <0>;
			nvidia,group = <1>;
			nvidia,frame-count = <64>;
			nvidia,frame-size = <320>;
		};
		ivccapture@4 {
			compatible = "nvidia,tegra186-camera-ivc-protocol-capture";
			nvidia,service = "capture";
			nvidia,version = <0>;
			nvidia,group = <1>;
			nvidia,frame-count = <512>;
			nvidia,frame-size = <64>;
		};
		diag@5 {
			compatible = "nvidia,tegra186-camera-diagnostics";
			nvidia,service = "diag";
			nvidia,version = <0>;
			nvidia,group = <1>;
			nvidia,frame-count = <1>;
			nvidia,frame-size = <64>;
		};
	};

	rtcpu_trace: tegra-rtcpu-trace {
		nvidia,enable-printk;
		nvidia,interval-ms = <50>;
		nvidia,log-prefix = "[RCE]";
	};

	capture_vi: tegra-capture-vi {
		compatible = "nvidia,tegra-camrtc-capture-vi";

		nvidia,vi-devices = <&vi0 &vi1>;
		nvidia,vi-mapping-size = <6>;
		nvidia,vi-mapping =
			<0 0>,
			<1 0>,
			<2 1>,
			<3 1>,
			<4 0>,
			<5 1>;
		nvidia,vi-mapping-names = "csi-stream-id", "vi-unit-id";
		nvidia,vi-max-channels = <72>;
	};

	reserved-memory {
		rce_resv: rce-reservation {
			iommu-addresses = <&tegra_rce 0x0 0x00000000 0x00000000 0xA0000000>,
					  <&tegra_rce 0x0 0xC0000000 0xffffffff 0x3fffffff>;
		};

		camdbg_reserved: camdbg_carveout {
			compatible = "nvidia,camdbg_carveout";
			size = <0 0x3200000>;
			alignment = <0 0x100000>;
			alloc-ranges = <0x1 0 0x1 0>;
			status = "disabled";
		};
	};
};
