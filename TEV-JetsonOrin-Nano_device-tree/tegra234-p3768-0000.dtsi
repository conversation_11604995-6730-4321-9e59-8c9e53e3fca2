// SPDX-License-Identifier: GPL-2.0

/ {
	compatible = "nvidia,p3768-0000";

	aliases {
		serial0 = &tcu;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	bus@0 {
		i2c@3160000 {
			status = "okay";

			eeprom@57 {
				compatible = "atmel,24c02";
				reg = <0x57>;

				label = "system";
				vcc-supply = <&vdd_1v8_sys>;
				address-width = <8>;
				pagesize = <8>;
				size = <256>;
				read-only;
			};
		};

		serial@31d0000 {
			current-speed = <115200>;
			status = "okay";
		};

		pwm@32a0000 {
			assigned-clocks = <&bpmp TEGRA234_CLK_PWM3>;
			assigned-clock-parents = <&bpmp TEGRA234_CLK_PLLP_OUT0>;
			status = "okay";
		};

		padctl@3520000 {
			status = "okay";

			pads {
				usb2 {
					lanes {
						usb2-0 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb2-1 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb2-2 {
							nvidia,function = "xusb";
							status = "okay";
						};
					};
				};

				usb3 {
					lanes {
						usb3-0 {
							nvidia,function = "xusb";
							status = "okay";
						};

						usb3-1 {
							nvidia,function = "xusb";
							status = "okay";
						};
					};
				};
			};

			ports {
				/* recovery port */
				usb2-0 {
					mode = "otg";
					vbus-supply = <&vdd_5v0_sys>;
					status = "okay";
					usb-role-switch;
				};

				/* hub */
				usb2-1 {
					mode = "host";
					vbus-supply = <&vdd_1v1_hub>;
					status = "okay";
				};

				/* M.2 Key-E */
				usb2-2 {
					mode = "host";
					vbus-supply = <&vdd_5v0_sys>;
					status = "okay";
				};

				/* hub */
				usb3-0 {
					nvidia,usb2-companion = <1>;
					status = "okay";
				};

				/* J5 */
				usb3-1 {
					nvidia,usb2-companion = <0>;
					status = "okay";
				};
			};
		};

		usb@3550000 {
			status = "okay";

			phys = <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-1}>;
			phy-names = "usb2-0", "usb3-0";
		};

		usb@3610000 {
			status = "okay";

			phys = <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-1}>,
			       <&{/bus@0/padctl@3520000/pads/usb2/lanes/usb2-2}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-0}>,
			       <&{/bus@0/padctl@3520000/pads/usb3/lanes/usb3-1}>;
			phy-names = "usb2-0", "usb2-1", "usb2-2", "usb3-0",
				    "usb3-1";
		};

		/* C8 - Ethernet */
		pcie@140a0000 {
			status = "okay";

			num-lanes = <2>;

			phys = <&p2u_gbe_2>, <&p2u_gbe_3>;
			phy-names = "p2u-0", "p2u-1";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;
			vpcie3v3-supply = <&vdd_3v3_pcie>;
		};

		/* C1 - M.2 Key-E */
		pcie@14100000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;

			phys = <&p2u_hsio_3>;
			phy-names = "p2u-0";
		};

		/* C4 - M.2 Key-M */
		pcie@14160000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;

			phys = <&p2u_hsio_4>, <&p2u_hsio_5>, <&p2u_hsio_6>,
			       <&p2u_hsio_7>;
			phy-names = "p2u-0", "p2u-1", "p2u-2", "p2u-3";
		};

		/* C7 - M.2 Key-M */
		pcie@141e0000 {
			status = "okay";

			vddio-pex-ctl-supply = <&vdd_1v8_ao>;

			phys = <&p2u_gbe_0>, <&p2u_gbe_1>;
			phy-names = "p2u-0", "p2u-1";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-force-recovery {
			label = "Force Recovery";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 0) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <BTN_1>;
		};

		key-power {
			label = "Power";
			gpios = <&gpio_aon TEGRA234_AON_GPIO(EE, 4) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_POWER>;
			wakeup-event-action = <EV_ACT_ASSERTED>;
			wakeup-source;
		};

		key-suspend {
			label = "Suspend";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 2) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_SLEEP>;
		};
	};

	fan: pwm-fan {
		compatible = "pwm-fan";
		pwms = <&pwm3 0 45334>;
		#cooling-cells = <2>;
	};

	vdd_1v8_sys: regulator-vdd-1v8-sys {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_SYS";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_1v1_hub: regulator-vdd-1v1-hub {
		compatible = "regulator-fixed";
		regulator-name = "VDD_AV10_HUB";
		regulator-min-microvolt = <1100000>;
		regulator-max-microvolt = <1100000>;
		vin-supply = <&vdd_5v0_sys>;
		regulator-always-on;
	};

	vdd_3v3_pcie: regulator-vdd-3v3-pcie {
		compatible = "regulator-fixed";
		regulator-name = "VDD_3V3_PCIE";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio_aon TEGRA234_AON_GPIO(AA, 5) GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	serial {
		status = "okay";
	};
};
