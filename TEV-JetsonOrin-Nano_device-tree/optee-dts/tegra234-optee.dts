/*
 * SPDX-License-Identifier: BSD-2-Clause
 *
 * SPDX-FileCopyrightText: Copyright (c) 2021-2024, NVIDIA CORPORATION. All rights reserved.
 */

/dts-v1/;

/ {
	#address-cells = <2>;
	#size-cells = <2>;

	/* MB2 fills the non-secure memory chucks here in order to
	 * enable the dynamic shared memory in OP-TEE.
	 * Example:
	 * nsec-memory@<xxx> {
	 * 	device_type = "memory";
	 *	reg = <xxx xxx xxx xxx>;
	 * };
	 */

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;

		/* MB2 will fill the DICE identities in the DICE node. */
		dice {
			compatible = "nvidia,dice-identity";
			status = "disabled";
			secure-status = "disabled";
			#address-cells = <2>;
			#size-cells = <2>;

			eca-csr@0 {
				compatible = "nvidia,dice-eca-csr";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};

			device-id-cert@0 {
				compatible = "nvidia,dice-device-id-cert";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};

			device-id-key-pub@0 {
				compatible = "nvidia,dice-device-id-key-pub";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};

			alias-key-cert@0 {
				compatible = "nvidia,dice-alias-key-cert";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};

			alias-key-pub@0 {
				compatible = "nvidia,dice-alias-key-pub";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};

			alias-key-priv@0 {
				compatible = "nvidia,dice-alias-key-priv";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};
		};

		/*
		 * The fTPM node is created to pass fTPM information from MB2 to OP-TEE.
		 * The reg attribute indicates the address and the size of the component,
		 * which will be filled by MB2 at runtime. All addresses are inside TZDRAM.
		 * The status of the nodes below will always be set to disabled and the
		 * secure-status will be set to okay by MB2 at runtime.
		 */
		ftpm {
			compatible = "nvidia,ftpm-contents";
			status = "disabled";
			secure-status = "disabled";
			#address-cells = <2>;
			#size-cells = <2>;

			/* This is the ftpm seed. */
			ftpm-seed@0 {
				compatible = "nvidia,ftpm-seed";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};

			/*
			 * This is the Firmware ID private key.
			 * OP-TEE needs it to sign the EK CSR.
			 */
			firmware-id-privkey@0 {
				compatible = "nvidia,ftpm-firmware-id-privkey";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};

			/* This is the Firmware ID certificate. */
			firmware-id-certificate@0 {
				compatible = "nvidia,ftpm-firmware-id-certificate";
				reg = <0 0 0 0>;
				status = "disabled";
				secure-status = "disabled";
			};
		};

		/* MB2 will fill the address and size of EKB blob. */
		ekb-blob@0 {
			compatible = "jetson-ekb-blob";
			reg = <0 0 0 0>;
		};

		/* MB2 will fill the address and size. */
		tpm-event-log@0 {
			compatible = "arm,tpm_event_log";
			tpm_event_log_addr = <0x0 0x0>;
			tpm_event_log_size = <0x0>;
		};
	};

	efuse@03810000 {
		compatible = "nvidia,tegra234-efuse";
		reg = <0x0 0x03810000 0x0 0x600>;
		status = "disabled";
		secure-status = "okay";
	};

	se0@03b50000 {
		compatible = "nvidia,tegra234-se0";
		reg = <0x0 0x03b50000 0x0 0x30000>;
		status = "disabled";
		secure-status = "okay";
	};

	rng1@03b70000 {
		compatible = "nvidia,tegra234-rng1";
		reg = <0x0 0x03b70000 0x0 0x10000>;
		status = "disabled";
		secure-status = "okay";
	};

	stmm-device-mappings {
		uuid = <0xed32d533 0x99e64209 0x9cc02d72 0xcdd998a7>;
		description = "UEFI-mm";

		device-regions {
			combuart-t234 {
				base-address = <0x00000000 0x0c198000>;
				pages-count = <0x1>;
				attributes = <0x3>; /* read-write */
			};

			qspi0-t234 {
				base-address = <0x00000000 0x03270000>;
				pages-count = <0x10>;
				attributes = <0x3>; /* read-write */
			};

			scratch-t234 {
				base-address = <0x00000000 0x0c390000>;
				pages-count = <0x2>;
				attributes = <0x3>; /* read-write */
			};
		};
	};
};
