// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

#include <dt-bindings/input/linux-event-codes.h>
#include <dt-bindings/input/gpio-keys.h>
#include "tegra234-p3701-0008.dtsi"
#include "tegra234-p3740-0002.dtsi"

/ {
	model = "NVIDIA IGX Orin Development Kit";
	compatible = "nvidia,p3740-0002+p3701-0008", "nvidia,p3701-0008", "nvidia,tegra234";

	aliases {
		serial0 = &tcu;
		serial1 = &uarta;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	bus@0 {
		serial@3100000 {
			compatible = "nvidia,tegra194-hsuart";
			reset-names = "serial";
			status = "okay";
		};

		i2c@3160000 {
			status = "okay";
		};

		i2c@3180000 {
			status = "okay";
		};

		i2c@3190000 {
			status = "okay";
		};

		i2c@31b0000 {
			status = "okay";
		};

		i2c@31c0000 {
			status = "okay";

		};

		i2c@31e0000 {
			status = "okay";
		};

		spi@3270000 {
			status = "okay";
		};

		hda@3510000 {
			nvidia,model = "NVIDIA IGX Orin HDA";
			status = "okay";
		};

		fuse@3810000 {
			status = "okay";
		};

		i2c@c240000 {
			status = "okay";
		};

		i2c@c250000 {
			status = "okay";
		};

		host1x@13e00000 {
			nvdec@15480000 {
				status = "okay";
			};
		};

		pcie@140e0000 {
			status = "okay";
			vddio-pex-ctl-supply = <&vdd_1v8_ls>;
			phys = <&p2u_gbe_4>, <&p2u_gbe_5>;
			phy-names = "p2u-0", "p2u-1";
		};

		pcie@14100000 {
			status = "okay";
			vddio-pex-ctl-supply = <&vdd_1v8_ao>;
			vpcie3v3-supply = <&vdd_3v3_wifi>;
			phys = <&p2u_hsio_3>;
			phy-names = "p2u-0";
		};

		pcie@14160000 {
			status = "okay";
			vddio-pex-ctl-supply = <&vdd_1v8_ao>;
			phys = <&p2u_hsio_7>, <&p2u_hsio_6>, <&p2u_hsio_5>,
			       <&p2u_hsio_4>;
			phy-names = "p2u-0", "p2u-1", "p2u-2", "p2u-3";
		};

		pcie@141a0000 {
			status = "okay";
			vddio-pex-ctl-supply = <&vdd_1v8_ls>;
			phys = <&p2u_nvhs_0>, <&p2u_nvhs_1>, <&p2u_nvhs_2>,
				<&p2u_nvhs_3>, <&p2u_nvhs_4>, <&p2u_nvhs_5>,
				<&p2u_nvhs_6>, <&p2u_nvhs_7>;
			phy-names = "p2u-0", "p2u-1", "p2u-2", "p2u-3", "p2u-4",
				    "p2u-5", "p2u-6", "p2u-7";
		};

		pcie@141e0000 {
			status = "okay";
			vddio-pex-ctl-supply = <&vdd_1v8_ls>;
			phys = <&p2u_gbe_0>, <&p2u_gbe_1>;
			phy-names = "p2u-0", "p2u-1";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		status = "okay";

		key-force-recovery {
			label = "Force Recovery";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 0) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <BTN_1>;
		};

		key-power {
			label = "Power";
			gpios = <&gpio_aon TEGRA234_AON_GPIO(EE, 4) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_POWER>;
			wakeup-event-action = <EV_ACT_ASSERTED>;
			wakeup-source;
		};

		key-suspend {
			label = "Suspend";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 2) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_SLEEP>;
		};
	};

	serial {
		status = "okay";
	};

	sound {
		status = "okay";

		compatible = "nvidia,tegra186-audio-graph-card";

		dais = /* ADMAIF (FE) Ports */
		       <&admaif0_port>, <&admaif1_port>, <&admaif2_port>, <&admaif3_port>,
		       <&admaif4_port>, <&admaif5_port>, <&admaif6_port>, <&admaif7_port>,
		       <&admaif8_port>, <&admaif9_port>, <&admaif10_port>, <&admaif11_port>,
		       <&admaif12_port>, <&admaif13_port>, <&admaif14_port>, <&admaif15_port>,
		       <&admaif16_port>, <&admaif17_port>, <&admaif18_port>, <&admaif19_port>,
		       /* XBAR Ports */
		       <&xbar_i2s1_port>, <&xbar_i2s2_port>, <&xbar_i2s4_port>,
		       <&xbar_i2s6_port>, <&xbar_dmic3_port>,
		       <&xbar_sfc1_in_port>, <&xbar_sfc2_in_port>,
		       <&xbar_sfc3_in_port>, <&xbar_sfc4_in_port>,
		       <&xbar_mvc1_in_port>, <&xbar_mvc2_in_port>,
		       <&xbar_amx1_in1_port>, <&xbar_amx1_in2_port>,
		       <&xbar_amx1_in3_port>, <&xbar_amx1_in4_port>,
		       <&xbar_amx2_in1_port>, <&xbar_amx2_in2_port>,
		       <&xbar_amx2_in3_port>, <&xbar_amx2_in4_port>,
		       <&xbar_amx3_in1_port>, <&xbar_amx3_in2_port>,
		       <&xbar_amx3_in3_port>, <&xbar_amx3_in4_port>,
		       <&xbar_amx4_in1_port>, <&xbar_amx4_in2_port>,
		       <&xbar_amx4_in3_port>, <&xbar_amx4_in4_port>,
		       <&xbar_adx1_in_port>, <&xbar_adx2_in_port>,
		       <&xbar_adx3_in_port>, <&xbar_adx4_in_port>,
		       <&xbar_mix_in1_port>, <&xbar_mix_in2_port>,
		       <&xbar_mix_in3_port>, <&xbar_mix_in4_port>,
		       <&xbar_mix_in5_port>, <&xbar_mix_in6_port>,
		       <&xbar_mix_in7_port>, <&xbar_mix_in8_port>,
		       <&xbar_mix_in9_port>, <&xbar_mix_in10_port>,
		       <&xbar_asrc_in1_port>, <&xbar_asrc_in2_port>,
		       <&xbar_asrc_in3_port>, <&xbar_asrc_in4_port>,
		       <&xbar_asrc_in5_port>, <&xbar_asrc_in6_port>,
		       <&xbar_asrc_in7_port>,
		       <&xbar_ope1_in_port>,
		       /* HW accelerators */
		       <&sfc1_out_port>, <&sfc2_out_port>,
		       <&sfc3_out_port>, <&sfc4_out_port>,
		       <&mvc1_out_port>, <&mvc2_out_port>,
		       <&amx1_out_port>, <&amx2_out_port>,
		       <&amx3_out_port>, <&amx4_out_port>,
		       <&adx1_out1_port>, <&adx1_out2_port>,
		       <&adx1_out3_port>, <&adx1_out4_port>,
		       <&adx2_out1_port>, <&adx2_out2_port>,
		       <&adx2_out3_port>, <&adx2_out4_port>,
		       <&adx3_out1_port>, <&adx3_out2_port>,
		       <&adx3_out3_port>, <&adx3_out4_port>,
		       <&adx4_out1_port>, <&adx4_out2_port>,
		       <&adx4_out3_port>, <&adx4_out4_port>,
		       <&mix_out1_port>, <&mix_out2_port>, <&mix_out3_port>,
		       <&mix_out4_port>, <&mix_out5_port>,
		       <&asrc_out1_port>, <&asrc_out2_port>, <&asrc_out3_port>,
		       <&asrc_out4_port>, <&asrc_out5_port>, <&asrc_out6_port>,
		       <&ope1_out_port>,
		       /* BE I/O Ports */
		       <&i2s1_port>, <&i2s2_port>, <&i2s4_port>, <&i2s6_port>,
		       <&dmic3_port>;

		label = "NVIDIA IGX Orin APE";

		widgets = "Microphone",	"CVB-RT MIC Jack",
			  "Microphone",	"CVB-RT MIC",
			  "Headphone",	"CVB-RT HP Jack",
			  "Speaker",	"CVB-RT SPK";

		routing = /* I2S4 <-> RT5640 */
			  "CVB-RT AIF1 Playback",	"I2S4 DAP-Playback",
			  "I2S4 DAP-Capture",		"CVB-RT AIF1 Capture",
			  /* RT5640 codec controls */
			  "CVB-RT HP Jack",		"CVB-RT HPOL",
			  "CVB-RT HP Jack",		"CVB-RT HPOR",
			  "CVB-RT IN1P",		"CVB-RT MIC Jack",
			  "CVB-RT IN2P",		"CVB-RT MIC Jack",
			  "CVB-RT IN2N",		"CVB-RT MIC Jack",
			  "CVB-RT IN3P",		"CVB-RT MIC Jack",
			  "CVB-RT SPK",			"CVB-RT SPOLP",
			  "CVB-RT SPK",			"CVB-RT SPORP",
			  "CVB-RT SPK",			"CVB-RT LOUTL",
			  "CVB-RT SPK",			"CVB-RT LOUTR",
			  "CVB-RT DMIC1",		"CVB-RT MIC",
			  "CVB-RT DMIC2",		"CVB-RT MIC";
	};
};
