// SPDX-License-Identifier: GPL-2.0

#include "tegra234.dtsi"

/ {
	compatible = "nvidia,p3767", "nvidia,tegra234";

	aliases {
		mmc0 = "/bus@0/mmc@3400000";
	};

	bus@0 {
		aconnect@2900000 {
			status = "okay";

			ahub@2900800 {
				status = "okay";

				i2s@2901100 {
					status = "okay";
				};

				i2s@2901300 {
					status = "okay";
				};
			};

			dma-controller@2930000 {
				status = "okay";
			};

			interrupt-controller@2a40000 {
				status = "okay";
			};
		};

		i2c@3160000 {
			status = "okay";

			eeprom@50 {
				compatible = "atmel,24c02";
				reg = <0x50>;

				label = "module";
				vcc-supply = <&vdd_1v8_hs>;
				address-width = <8>;
				pagesize = <8>;
				size = <256>;
				read-only;
			};
		};

		spi@3270000 {
			status = "okay";

			flash@0 {
				compatible = "jedec,spi-nor";
				reg = <0>;
				spi-max-frequency = <102000000>;
				spi-tx-bus-width = <4>;
				spi-rx-bus-width = <4>;
			};
		};

		/*
		 * This only exists on Jetson Orin Nano Developer Kit (SKU 5)
		 * but UEFI needs this and will remove it on devices where it
		 * doesn't exist.
		 */
		mmc@3400000 {
			status = "okay";
			bus-width = <4>;
			cd-gpios = <&gpio TEGRA234_MAIN_GPIO(G, 7) GPIO_ACTIVE_LOW>;
			disable-wp;
		};

		hda@3510000 {
			status = "okay";
		};

		padctl@3520000 {
			vclamp-usb-supply = <&vdd_1v8_ao>;
			avdd-usb-supply = <&vdd_3v3_ao>;
		};

		rtc@c2a0000 {
			status = "okay";
		};

		pmc@c360000 {
			nvidia,invert-interrupt;
		};
	};

	vdd_5v0_sys: regulator-vdd-5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "VDD_5V0_SYS";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};

	vdd_1v8_hs: regulator-vdd-1v8-hs {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_HS";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_1v8_ao: regulator-vdd-1v8-ao {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_AO";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
		vin-supply = <&vdd_5v0_sys>;
	};

	vdd_3v3_ao: regulator-vdd-3v3-ao {
		compatible = "regulator-fixed";
		regulator-name = "VDD_3V3_AO";
		regulator-min-microvolt = <33000000>;
		regulator-max-microvolt = <33000000>;
		regulator-always-on;
		vin-supply = <&vdd_5v0_sys>;
	};

	sound {
		compatible = "nvidia,tegra186-audio-graph-card";
		status = "okay";

		dais = /* ADMAIF (FE) Ports */
		       <&admaif0_port>, <&admaif1_port>, <&admaif2_port>, <&admaif3_port>,
		       <&admaif4_port>, <&admaif5_port>, <&admaif6_port>, <&admaif7_port>,
		       <&admaif8_port>, <&admaif9_port>, <&admaif10_port>, <&admaif11_port>,
		       <&admaif12_port>, <&admaif13_port>, <&admaif14_port>, <&admaif15_port>,
		       <&admaif16_port>, <&admaif17_port>, <&admaif18_port>, <&admaif19_port>,
		       /* XBAR Ports */
		       <&xbar_i2s2_port>, <&xbar_i2s4_port>,
		       <&xbar_sfc1_in_port>, <&xbar_sfc2_in_port>,
		       <&xbar_sfc3_in_port>, <&xbar_sfc4_in_port>,
		       <&xbar_mvc1_in_port>, <&xbar_mvc2_in_port>,
		       <&xbar_amx1_in1_port>, <&xbar_amx1_in2_port>,
		       <&xbar_amx1_in3_port>, <&xbar_amx1_in4_port>,
		       <&xbar_amx2_in1_port>, <&xbar_amx2_in2_port>,
		       <&xbar_amx2_in3_port>, <&xbar_amx2_in4_port>,
		       <&xbar_amx3_in1_port>, <&xbar_amx3_in2_port>,
		       <&xbar_amx3_in3_port>, <&xbar_amx3_in4_port>,
		       <&xbar_amx4_in1_port>, <&xbar_amx4_in2_port>,
		       <&xbar_amx4_in3_port>, <&xbar_amx4_in4_port>,
		       <&xbar_adx1_in_port>, <&xbar_adx2_in_port>,
		       <&xbar_adx3_in_port>, <&xbar_adx4_in_port>,
		       <&xbar_mix_in1_port>, <&xbar_mix_in2_port>,
		       <&xbar_mix_in3_port>, <&xbar_mix_in4_port>,
		       <&xbar_mix_in5_port>, <&xbar_mix_in6_port>,
		       <&xbar_mix_in7_port>, <&xbar_mix_in8_port>,
		       <&xbar_mix_in9_port>, <&xbar_mix_in10_port>,
		       <&xbar_asrc_in1_port>, <&xbar_asrc_in2_port>,
		       <&xbar_asrc_in3_port>, <&xbar_asrc_in4_port>,
		       <&xbar_asrc_in5_port>, <&xbar_asrc_in6_port>,
		       <&xbar_asrc_in7_port>,
		       <&xbar_ope1_in_port>,
		       /* HW accelerators */
		       <&sfc1_out_port>, <&sfc2_out_port>,
		       <&sfc3_out_port>, <&sfc4_out_port>,
		       <&mvc1_out_port>, <&mvc2_out_port>,
		       <&amx1_out_port>, <&amx2_out_port>,
		       <&amx3_out_port>, <&amx4_out_port>,
		       <&adx1_out1_port>, <&adx1_out2_port>,
		       <&adx1_out3_port>, <&adx1_out4_port>,
		       <&adx2_out1_port>, <&adx2_out2_port>,
		       <&adx2_out3_port>, <&adx2_out4_port>,
		       <&adx3_out1_port>, <&adx3_out2_port>,
		       <&adx3_out3_port>, <&adx3_out4_port>,
		       <&adx4_out1_port>, <&adx4_out2_port>,
		       <&adx4_out3_port>, <&adx4_out4_port>,
		       <&mix_out1_port>, <&mix_out2_port>, <&mix_out3_port>,
		       <&mix_out4_port>, <&mix_out5_port>,
		       <&asrc_out1_port>, <&asrc_out2_port>, <&asrc_out3_port>,
		       <&asrc_out4_port>, <&asrc_out5_port>, <&asrc_out6_port>,
		       <&ope1_out_port>,
		       /* BE I/O Ports */
		       <&i2s2_port>, <&i2s4_port>;
	};

	thermal-zones {
		tj-thermal {
			polling-delay = <1000>;
			polling-delay-passive = <1000>;
			status = "okay";

			trips {
				tj_trip_active0: active-0 {
					temperature = <35000>;
					hysteresis = <4000>;
					type = "active";
				};

				tj_trip_active1: active-1 {
					temperature = <74000>;
					hysteresis = <4000>;
					type = "active";
				};

				tj_trip_active2: active-2 {
					temperature = <95000>;
					hysteresis = <4000>;
					type = "active";
				};
			};
		};
	};
};
