/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Copyright (c) 2023, NVIDIA CORPORATION.  All rights reserved.
 */

#ifndef _DT_BINDINGS_P2U_TEGRAT234_P2U_H
#define _DT_BINDINGS_P2U_TEGRAT234_P2U_H

#define TEGRA234_P2U_LANE_ID0		0
#define TEGRA234_P2U_LANE_ID1		1
#define TEGRA234_P2U_LANE_ID2		2
#define TEGRA234_P2U_LANE_ID3		3
#define TEGRA234_P2U_LANE_ID4		4
#define TEGRA234_P2U_LANE_ID5		5
#define TEGRA234_P2U_LANE_ID6		6
#define TEGRA234_P2U_LANE_ID7		7
#define TEGRA234_P2U_LANE_ID8		8
#define TEGRA234_P2U_LANE_ID9		9
#define TEGRA234_P2U_LANE_ID10		10
#define TEGRA234_P2U_LANE_ID11		11
#define TEGRA234_P2U_LANE_ID12		12
#define TEGRA234_P2U_LANE_ID13		13
#define TEGRA234_P2U_LANE_ID14		14
#define TEGRA234_P2U_LANE_ID15		15
#define TEGRA234_P2U_LANE_ID16		16
#define TEGRA234_P2U_LANE_ID17		17
#define TEGRA234_P2U_LANE_ID18		18
#define TEGRA234_P2U_LANE_ID19		19
#define TEGRA234_P2U_LANE_ID20		20
#define TEGRA234_P2U_LANE_ID21		21
#define TEGRA234_P2U_LANE_ID22		22
#define TEGRA234_P2U_LANE_ID23		23

#endif /* _DT_BINDINGS_P2U_TEGRAT234_P2U_H */
