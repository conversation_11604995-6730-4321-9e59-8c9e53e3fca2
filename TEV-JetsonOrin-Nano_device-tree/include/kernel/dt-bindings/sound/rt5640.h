/* SPDX-License-Identifier: GPL-2.0 */
#ifndef __DT_RT5640_H
#define __DT_RT5640_H

#define RT5640_DMIC1_DATA_PIN_NONE	0
#define RT5640_DMIC1_DATA_PIN_IN1P	1
#define RT5640_DMIC1_DATA_PIN_GPIO3	2

#define RT5640_DMIC2_DATA_PIN_NONE	0
#define RT5640_DMIC2_DATA_PIN_IN1N	1
#define RT5640_DMIC2_DATA_PIN_GPIO4	2

#define RT5640_JD_SRC_GPIO1		1
#define RT5640_JD_SRC_JD1_IN4P		2
#define RT5640_JD_SRC_JD2_IN4N		3
#define RT5640_JD_SRC_GPIO2		4
#define RT5640_JD_SRC_GPIO3		5
#define RT5640_JD_SRC_GPIO4		6
#define RT5640_JD_SRC_HDA_HEADER	7

#define RT5640_OVCD_SF_0P5		0
#define RT5640_OVCD_SF_0P75		1
#define RT5640_OVCD_SF_1P0		2
#define RT5640_OVCD_SF_1P5		3

#endif /* __DT_RT5640_H */
