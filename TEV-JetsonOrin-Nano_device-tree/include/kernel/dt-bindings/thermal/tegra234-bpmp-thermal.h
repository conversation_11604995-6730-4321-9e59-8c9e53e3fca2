/* SPDX-License-Identifier: GPL-2.0 */
/*
 * This header provides constants for binding nvidia,tegra234-bpmp-thermal.
 */

#ifndef _DT_BINDINGS_THERMAL_TEGRA234_BPMP_THERMAL_H
#define _DT_BINDINGS_THERMAL_TEGRA234_BPMP_THERMAL_H

#define TEGRA234_BPMP_THERMAL_ZONE_CPU		0
#define TEGRA234_BPMP_THERMAL_ZONE_GPU		1
#define TEGRA234_BPMP_THERMAL_ZONE_CV0		2
#define TEGRA234_BPMP_THERMAL_ZONE_CV1		3
#define TEGRA234_BPMP_THERMAL_ZONE_CV2		4
#define TEGRA234_BPMP_THERMAL_ZONE_SOC0		5
#define TEGRA234_BPMP_THERMAL_ZONE_SOC1		6
#define TEGRA234_BPMP_THERMAL_ZONE_SOC2		7
#define TEGRA234_BPMP_THERMAL_ZONE_TJ_MAX	8

#endif
