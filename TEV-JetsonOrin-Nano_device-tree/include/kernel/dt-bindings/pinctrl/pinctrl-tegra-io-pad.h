/* SPDX-License-Identifier: GPL-2.0 */
/*
 * pinctrl-tegra-io-pad.h: Tegra I/O pad source voltage configuration constants
 * pinctrl bindings.
 *
 * Copyright (c) 2018, NVIDIA CORPORATION.  All rights reserved.
 *
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 */

#ifndef _DT_BINDINGS_PINCTRL_TEGRA_IO_PAD_H
#define _DT_BINDINGS_PINCTRL_TEGRA_IO_PAD_H

/* Voltage levels of the I/O pad's source rail */
#define TEGRA_IO_PAD_VOLTAGE_1V8	0
#define TEGRA_IO_PAD_VOLTAGE_3V3	1

#endif
