// SPDX-License-Identifier: GPL-2.0

/ {
	compatible = "nvidia,p3701", "nvidia,tegra234";

	aliases {
		mmc0 = "/bus@0/mmc@3460000";
		mmc1 = "/bus@0/mmc@3400000";
	};

	bus@0 {
		aconnect@2900000 {
			status = "okay";

			ahub@2900800 {
				status = "okay";

				i2s@2901000 {
					status = "okay";
				};

				i2s@2901100 {
					status = "okay";
				};

				i2s@2901300 {
					status = "okay";
				};

				i2s@2901500 {
					status = "okay";
				};

				dmic@2904200 {
					status = "okay";
				};
			};

			dma-controller@2930000 {
				status = "okay";
			};

			interrupt-controller@2a40000 {
				status = "okay";
			};
		};
	};
};
