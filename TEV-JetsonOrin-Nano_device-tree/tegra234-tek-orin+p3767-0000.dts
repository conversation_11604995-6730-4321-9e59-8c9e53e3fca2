// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

#include <dt-bindings/input/linux-event-codes.h>
#include <dt-bindings/input/gpio-keys.h>

#include "tegra234-p3767.dtsi"
#include "tegra234-tek-orin.dtsi"

/ {
	compatible = "nvidia,p3768-0000+p3767-0000", "nvidia,p3767-0000", "nvidia,tegra234";
	model = "Technexion Jetson Orin NX Developer Kit";

	aliases {
		serial1 = &uarta;
		serial2 = &uarte;
	};

	bus@0 {
		serial@3100000 {
			compatible = "nvidia,tegra194-hsuart";
			reset-names = "serial";
			status = "okay";
		};

		serial@3140000 {
			compatible = "nvidia,tegra194-hsuart";
			reset-names = "serial";
			status = "okay";
		};

		pwm@32a0000 {
			assigned-clocks = <&bpmp TEGRA234_CLK_PWM3>;
			assigned-clock-parents = <&bpmp TEGRA234_CLK_PLLP_OUT0>;
			status = "okay";
		};

		hda@3510000 {
			nvidia,model = "NVIDIA Jetson Orin NX HDA";
		};

		padctl@3520000 {
			status = "okay";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		key-force-recovery {
			label = "Force Recovery";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 0) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <BTN_1>;
		};

		key-power {
			label = "Power";
			gpios = <&gpio_aon TEGRA234_AON_GPIO(EE, 4) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_POWER>;
			wakeup-event-action = <EV_ACT_ASSERTED>;
			wakeup-source;
		};

		key-suspend {
			label = "Suspend";
			gpios = <&gpio TEGRA234_MAIN_GPIO(G, 2) GPIO_ACTIVE_LOW>;
			linux,input-type = <EV_KEY>;
			linux,code = <KEY_SLEEP>;
		};
	};

	pwm-fan {
		cooling-levels = <0 88 187 255>;
	};

	vdd_3v3_pcie: regulator-vdd-3v3-pcie {
		compatible = "regulator-fixed";
		regulator-name = "VDD_3V3_PCIE";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio_aon TEGRA234_AON_GPIO(AA, 5) GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	sound {
		label = "NVIDIA Jetson Orin NX APE";
	};

	thermal-zones {
		tj-thermal {
			cooling-maps {
				map-active-0 {
					cooling-device = <&fan 0 1>;
					trip = <&tj_trip_active0>;
				};

				map-active-1 {
					cooling-device = <&fan 1 2>;
					trip = <&tj_trip_active1>;
				};

				map-active-2 {
					cooling-device = <&fan 2 3>;
					trip = <&tj_trip_active2>;
				};
			};
		};
	};
};
