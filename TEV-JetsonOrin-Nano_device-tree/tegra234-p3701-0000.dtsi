// SPDX-License-Identifier: GPL-2.0

#include "tegra234.dtsi"
#include "tegra234-p3701.dtsi"

/ {
	model = "NVIDIA Jetson AGX Orin";
	compatible = "nvidia,p3701-0000", "nvidia,tegra234";

	bus@0 {
		i2c@3160000 {
			status = "okay";

			eeprom@50 {
				compatible = "atmel,24c02";
				reg = <0x50>;

				label = "module";
				vcc-supply = <&vdd_1v8_hs>;
				address-width = <8>;
				pagesize = <8>;
				size = <256>;
				read-only;
			};
		};

		spi@3270000 {
			status = "okay";

			flash@0 {
				compatible = "jedec,spi-nor";
				reg = <0>;
				spi-max-frequency = <102000000>;
				spi-tx-bus-width = <4>;
				spi-rx-bus-width = <4>;
			};
		};

		mmc@3400000 {
			status = "okay";
			bus-width = <4>;
			cd-gpios = <&gpio TEGRA234_MAIN_GPIO(G, 7) GPIO_ACTIVE_LOW>;
			disable-wp;
		};

		mmc@3460000 {
			status = "okay";
			bus-width = <8>;
			non-removable;
		};

		padctl@3520000 {
			vclamp-usb-supply = <&vdd_1v8_ao>;
			avdd-usb-supply = <&vdd_3v3_ao>;

			ports {
				usb2-0 {
					vbus-supply = <&vdd_5v0_sys>;
				};

				usb2-1 {
					vbus-supply = <&vdd_5v0_sys>;
				};

				usb2-2 {
					vbus-supply = <&vdd_5v0_sys>;
				};

				usb2-3 {
					vbus-supply = <&vdd_5v0_sys>;
				};
			};
		};

		rtc@c2a0000 {
			status = "okay";
		};

		pmc@c360000 {
			nvidia,invert-interrupt;
		};
	};

	vdd_5v0_sys: regulator-vdd-5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "VIN_SYS_5V0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vdd_1v8_ls: regulator-vdd-1v8-ls {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_LS";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_1v8_hs: regulator-vdd-1v8-hs {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_HS";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_1v8_ao: regulator-vdd-1v8-ao {
		compatible = "regulator-fixed";
		regulator-name = "VDD_1V8_AO";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	vdd_3v3_ao: regulator-vdd-3v3-ao {
		compatible = "regulator-fixed";
		regulator-name = "VDD_3V3_AO";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	vdd_3v3_pcie: regulator-vdd-3v3-pcie {
		compatible = "regulator-fixed";
		regulator-name = "VDD_3V3_PCIE";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio TEGRA234_MAIN_GPIO(H, 4) GPIO_ACTIVE_HIGH>;
		regulator-boot-on;
		enable-active-high;
	};

	vdd_12v_pcie: regulator-vdd-12v-pcie {
		compatible = "regulator-fixed";
		regulator-name = "VDD_12V_PCIE";
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
		gpio = <&gpio TEGRA234_MAIN_GPIO(A, 1) GPIO_ACTIVE_LOW>;
		regulator-boot-on;
	};

	thermal-zones {
		tj-thermal {
			polling-delay = <1000>;
			polling-delay-passive = <1000>;
			status = "okay";

			trips {
				tj_trip_active0: active-0 {
					temperature = <75000>;
					hysteresis = <4000>;
					type = "active";
				};

				tj_trip_active1: active-1 {
					temperature = <95000>;
					hysteresis = <4000>;
					type = "active";
				};
			};
		};
	};
};
